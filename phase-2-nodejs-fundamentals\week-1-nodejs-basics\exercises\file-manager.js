// Week 1 Exercise: File Manager
// Build a comprehensive file management utility using Node.js core modules

const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');
const os = require('os');

/**
 * FileManager class - Comprehensive file operations utility
 * Demonstrates: fs module, path module, crypto module, error handling
 */
class FileManager {
    constructor(baseDirectory = './data') {
        this.baseDirectory = baseDirectory;
        this.init();
    }
    
    /**
     * Initialize the file manager
     */
    async init() {
        try {
            await this.ensureDirectory(this.baseDirectory);
            console.log(`FileManager initialized with base directory: ${this.baseDirectory}`);
        } catch (error) {
            console.error('Failed to initialize FileManager:', error.message);
        }
    }
    
    /**
     * Ensure a directory exists, create if it doesn't
     * @param {string} dirPath - Directory path
     */
    async ensureDirectory(dirPath) {
        try {
            await fs.mkdir(dirPath, { recursive: true });
        } catch (error) {
            if (error.code !== 'EEXIST') {
                throw error;
            }
        }
    }
    
    /**
     * Write data to a file
     * @param {string} filename - Name of the file
     * @param {string|object} data - Data to write
     * @param {object} options - Write options
     */
    async writeFile(filename, data, options = {}) {
        try {
            const filePath = path.join(this.baseDirectory, filename);
            
            // Ensure directory exists
            await this.ensureDirectory(path.dirname(filePath));
            
            // Convert object to JSON if needed
            const content = typeof data === 'object' ? JSON.stringify(data, null, 2) : data;
            
            // Create backup if file exists and backup option is enabled
            if (options.backup && await this.fileExists(filePath)) {
                await this.createBackup(filePath);
            }
            
            await fs.writeFile(filePath, content, 'utf8');
            
            console.log(`File written successfully: ${filename}`);
            return { success: true, path: filePath };
        } catch (error) {
            console.error(`Error writing file ${filename}:`, error.message);
            return { success: false, error: error.message };
        }
    }
    
    /**
     * Read data from a file
     * @param {string} filename - Name of the file
     * @param {object} options - Read options
     */
    async readFile(filename, options = {}) {
        try {
            const filePath = path.join(this.baseDirectory, filename);
            const content = await fs.readFile(filePath, 'utf8');
            
            // Parse JSON if requested
            if (options.parseJSON) {
                try {
                    return { success: true, data: JSON.parse(content) };
                } catch (parseError) {
                    return { success: false, error: 'Invalid JSON format' };
                }
            }
            
            return { success: true, data: content };
        } catch (error) {
            if (error.code === 'ENOENT') {
                return { success: false, error: 'File not found' };
            }
            return { success: false, error: error.message };
        }
    }
    
    /**
     * Check if a file exists
     * @param {string} filePath - Path to the file
     */
    async fileExists(filePath) {
        try {
            await fs.access(filePath);
            return true;
        } catch {
            return false;
        }
    }
    
    /**
     * Get file information
     * @param {string} filename - Name of the file
     */
    async getFileInfo(filename) {
        try {
            const filePath = path.join(this.baseDirectory, filename);
            const stats = await fs.stat(filePath);
            
            return {
                success: true,
                info: {
                    name: filename,
                    path: filePath,
                    size: stats.size,
                    sizeFormatted: this.formatFileSize(stats.size),
                    isFile: stats.isFile(),
                    isDirectory: stats.isDirectory(),
                    created: stats.birthtime,
                    modified: stats.mtime,
                    accessed: stats.atime
                }
            };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }
    
    /**
     * List files in a directory
     * @param {string} directory - Directory to list (relative to base)
     */
    async listFiles(directory = '.') {
        try {
            const dirPath = path.join(this.baseDirectory, directory);
            const items = await fs.readdir(dirPath);
            
            const fileList = await Promise.all(
                items.map(async (item) => {
                    const itemPath = path.join(dirPath, item);
                    const stats = await fs.stat(itemPath);
                    
                    return {
                        name: item,
                        type: stats.isDirectory() ? 'directory' : 'file',
                        size: stats.size,
                        sizeFormatted: this.formatFileSize(stats.size),
                        modified: stats.mtime
                    };
                })
            );
            
            return { success: true, files: fileList };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }
    
    /**
     * Delete a file
     * @param {string} filename - Name of the file to delete
     * @param {object} options - Delete options
     */
    async deleteFile(filename, options = {}) {
        try {
            const filePath = path.join(this.baseDirectory, filename);
            
            // Create backup before deletion if requested
            if (options.backup) {
                await this.createBackup(filePath);
            }
            
            await fs.unlink(filePath);
            console.log(`File deleted successfully: ${filename}`);
            return { success: true };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }
    
    /**
     * Copy a file
     * @param {string} source - Source filename
     * @param {string} destination - Destination filename
     */
    async copyFile(source, destination) {
        try {
            const sourcePath = path.join(this.baseDirectory, source);
            const destPath = path.join(this.baseDirectory, destination);
            
            // Ensure destination directory exists
            await this.ensureDirectory(path.dirname(destPath));
            
            await fs.copyFile(sourcePath, destPath);
            console.log(`File copied: ${source} -> ${destination}`);
            return { success: true };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }
    
    /**
     * Move/rename a file
     * @param {string} source - Source filename
     * @param {string} destination - Destination filename
     */
    async moveFile(source, destination) {
        try {
            const sourcePath = path.join(this.baseDirectory, source);
            const destPath = path.join(this.baseDirectory, destination);
            
            // Ensure destination directory exists
            await this.ensureDirectory(path.dirname(destPath));
            
            await fs.rename(sourcePath, destPath);
            console.log(`File moved: ${source} -> ${destination}`);
            return { success: true };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }
    
    /**
     * Create a backup of a file
     * @param {string} filePath - Path to the file to backup
     */
    async createBackup(filePath) {
        try {
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const ext = path.extname(filePath);
            const name = path.basename(filePath, ext);
            const dir = path.dirname(filePath);
            
            const backupPath = path.join(dir, 'backups', `${name}.backup.${timestamp}${ext}`);
            
            // Ensure backup directory exists
            await this.ensureDirectory(path.dirname(backupPath));
            
            await fs.copyFile(filePath, backupPath);
            console.log(`Backup created: ${backupPath}`);
            return { success: true, backupPath };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }
    
    /**
     * Generate file hash
     * @param {string} filename - Name of the file
     * @param {string} algorithm - Hash algorithm (md5, sha1, sha256)
     */
    async generateFileHash(filename, algorithm = 'sha256') {
        try {
            const filePath = path.join(this.baseDirectory, filename);
            const content = await fs.readFile(filePath);
            
            const hash = crypto.createHash(algorithm);
            hash.update(content);
            const hashValue = hash.digest('hex');
            
            return { success: true, hash: hashValue, algorithm };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }
    
    /**
     * Search for files by pattern
     * @param {string} pattern - Search pattern (regex)
     * @param {string} directory - Directory to search in
     */
    async searchFiles(pattern, directory = '.') {
        try {
            const regex = new RegExp(pattern, 'i');
            const { success, files } = await this.listFiles(directory);
            
            if (!success) {
                return { success: false, error: 'Failed to list files' };
            }
            
            const matchingFiles = files.filter(file => regex.test(file.name));
            
            return { success: true, matches: matchingFiles };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }
    
    /**
     * Get directory size
     * @param {string} directory - Directory to analyze
     */
    async getDirectorySize(directory = '.') {
        try {
            const dirPath = path.join(this.baseDirectory, directory);
            let totalSize = 0;
            
            const calculateSize = async (currentPath) => {
                const items = await fs.readdir(currentPath);
                
                for (const item of items) {
                    const itemPath = path.join(currentPath, item);
                    const stats = await fs.stat(itemPath);
                    
                    if (stats.isFile()) {
                        totalSize += stats.size;
                    } else if (stats.isDirectory()) {
                        await calculateSize(itemPath);
                    }
                }
            };
            
            await calculateSize(dirPath);
            
            return {
                success: true,
                size: totalSize,
                sizeFormatted: this.formatFileSize(totalSize)
            };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }
    
    /**
     * Clean up old backup files
     * @param {number} maxAge - Maximum age in days
     */
    async cleanupBackups(maxAge = 30) {
        try {
            const backupDir = path.join(this.baseDirectory, 'backups');
            
            if (!await this.fileExists(backupDir)) {
                return { success: true, message: 'No backup directory found' };
            }
            
            const files = await fs.readdir(backupDir);
            const cutoffDate = new Date(Date.now() - maxAge * 24 * 60 * 60 * 1000);
            let deletedCount = 0;
            
            for (const file of files) {
                const filePath = path.join(backupDir, file);
                const stats = await fs.stat(filePath);
                
                if (stats.mtime < cutoffDate) {
                    await fs.unlink(filePath);
                    deletedCount++;
                }
            }
            
            return {
                success: true,
                message: `Cleaned up ${deletedCount} old backup files`
            };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }
    
    /**
     * Format file size in human-readable format
     * @param {number} bytes - Size in bytes
     */
    formatFileSize(bytes) {
        const units = ['B', 'KB', 'MB', 'GB', 'TB'];
        let size = bytes;
        let unitIndex = 0;
        
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        
        return `${size.toFixed(2)} ${units[unitIndex]}`;
    }
    
    /**
     * Get system information
     */
    getSystemInfo() {
        return {
            platform: os.platform(),
            architecture: os.arch(),
            hostname: os.hostname(),
            totalMemory: this.formatFileSize(os.totalmem()),
            freeMemory: this.formatFileSize(os.freemem()),
            uptime: `${Math.floor(os.uptime() / 3600)} hours`,
            nodeVersion: process.version,
            baseDirectory: path.resolve(this.baseDirectory)
        };
    }
}

// Example usage and testing
async function demonstrateFileManager() {
    console.log('=== File Manager Demo ===\n');
    
    const fm = new FileManager('./demo-data');
    
    // Test writing files
    console.log('1. Writing test files...');
    await fm.writeFile('test.txt', 'Hello, Node.js File Manager!');
    await fm.writeFile('data.json', { users: [{ id: 1, name: 'John' }] });
    
    // Test reading files
    console.log('\n2. Reading files...');
    const textResult = await fm.readFile('test.txt');
    console.log('Text file:', textResult);
    
    const jsonResult = await fm.readFile('data.json', { parseJSON: true });
    console.log('JSON file:', jsonResult);
    
    // Test file information
    console.log('\n3. Getting file information...');
    const fileInfo = await fm.getFileInfo('test.txt');
    console.log('File info:', fileInfo);
    
    // Test listing files
    console.log('\n4. Listing files...');
    const fileList = await fm.listFiles();
    console.log('File list:', fileList);
    
    // Test file operations
    console.log('\n5. Testing file operations...');
    await fm.copyFile('test.txt', 'test-copy.txt');
    await fm.moveFile('test-copy.txt', 'renamed-test.txt');
    
    // Test hash generation
    console.log('\n6. Generating file hash...');
    const hashResult = await fm.generateFileHash('test.txt');
    console.log('File hash:', hashResult);
    
    // Test directory size
    console.log('\n7. Getting directory size...');
    const sizeResult = await fm.getDirectorySize();
    console.log('Directory size:', sizeResult);
    
    // Test system information
    console.log('\n8. System information:');
    const systemInfo = fm.getSystemInfo();
    console.log(systemInfo);
    
    console.log('\n=== File Manager Demo Complete ===');
}

// Run the demonstration
if (require.main === module) {
    demonstrateFileManager().catch(console.error);
}

module.exports = FileManager;
