// Week 1 Day 1: Node.js Event Loop Demonstration
// This file demonstrates how the Node.js event loop works

console.log('=== Node.js Event Loop Demo ===\n');

// 1. Basic Event Loop Example
console.log('1. BASIC EVENT LOOP EXAMPLE');
console.log('Start of script');

// Immediate execution
console.log('Synchronous operation 1');

// Asynchronous operations
setTimeout(() => {
    console.log('setTimeout with 0ms delay');
}, 0);

setImmediate(() => {
    console.log('setImmediate callback');
});

process.nextTick(() => {
    console.log('process.nextTick callback');
});

Promise.resolve().then(() => {
    console.log('Promise resolved');
});

console.log('Synchronous operation 2');
console.log('End of script\n');

// 2. Event Loop Phases Demonstration
console.log('2. EVENT LOOP PHASES DEMO');

setTimeout(() => console.log('Timer 1'), 0);
setTimeout(() => console.log('Timer 2'), 0);

setImmediate(() => console.log('Immediate 1'));
setImmediate(() => console.log('Immediate 2'));

process.nextTick(() => console.log('NextTick 1'));
process.nextTick(() => console.log('NextTick 2'));

Promise.resolve().then(() => console.log('Promise 1'));
Promise.resolve().then(() => console.log('Promise 2'));

// 3. Blocking vs Non-blocking Operations
console.log('\n3. BLOCKING VS NON-BLOCKING DEMO');

// Non-blocking file operation
const fs = require('fs');

console.log('Before async file read');
fs.readFile(__filename, 'utf8', (err, data) => {
    if (err) {
        console.error('Error reading file:', err.message);
        return;
    }
    console.log('Async file read completed - file size:', data.length, 'characters');
});
console.log('After async file read (non-blocking)');

// 4. Event Loop with I/O Operations
console.log('\n4. I/O OPERATIONS IN EVENT LOOP');

// File system operation
fs.readFile('package.json', 'utf8', (err, data) => {
    if (err) {
        console.log('File read error (expected if package.json not found)');
    } else {
        console.log('Package.json read successfully');
    }
});

// Network-like operation simulation
const http = require('http');
const server = http.createServer((req, res) => {
    res.writeHead(200, { 'Content-Type': 'text/plain' });
    res.end('Hello from event loop demo server!');
});

// Start server briefly to demonstrate I/O
server.listen(0, () => {
    const port = server.address().port;
    console.log(`Demo server started on port ${port}`);
    
    // Make a request to our own server
    const request = http.get(`http://localhost:${port}`, (response) => {
        let data = '';
        response.on('data', chunk => {
            data += chunk;
        });
        response.on('end', () => {
            console.log('HTTP response received:', data);
            server.close(() => {
                console.log('Demo server closed');
            });
        });
    });
    
    request.on('error', (err) => {
        console.error('HTTP request error:', err.message);
        server.close();
    });
});

// 5. Microtasks vs Macrotasks
console.log('\n5. MICROTASKS VS MACROTASKS');

setTimeout(() => console.log('Macrotask: setTimeout'), 0);

Promise.resolve().then(() => {
    console.log('Microtask: Promise 1');
    return Promise.resolve();
}).then(() => {
    console.log('Microtask: Promise 2');
});

process.nextTick(() => {
    console.log('Microtask: nextTick 1');
    process.nextTick(() => {
        console.log('Microtask: nextTick 2 (nested)');
    });
});

setImmediate(() => console.log('Macrotask: setImmediate'));

// 6. Event Loop Phases Order
console.log('\n6. EVENT LOOP PHASES ORDER');

// Timer phase
setTimeout(() => console.log('Phase 1: Timer'), 1);

// Poll phase (I/O)
fs.readFile(__filename, () => {
    console.log('Phase 2: Poll (I/O callback)');
    
    // Check phase
    setImmediate(() => console.log('Phase 3: Check (setImmediate)'));
    
    // Timer phase (next iteration)
    setTimeout(() => console.log('Phase 4: Timer (next iteration)'), 0);
});

// 7. CPU-intensive vs I/O-intensive
console.log('\n7. CPU vs I/O INTENSIVE OPERATIONS');

// CPU-intensive operation (blocking)
function cpuIntensiveTask() {
    console.log('Starting CPU-intensive task...');
    const start = Date.now();
    
    // Simulate CPU work
    let result = 0;
    for (let i = 0; i < 1000000; i++) {
        result += Math.random();
    }
    
    const end = Date.now();
    console.log(`CPU task completed in ${end - start}ms, result: ${result.toFixed(2)}`);
}

// I/O-intensive operation (non-blocking)
function ioIntensiveTask() {
    console.log('Starting I/O-intensive task...');
    const start = Date.now();
    
    fs.readFile(__filename, 'utf8', (err, data) => {
        const end = Date.now();
        if (err) {
            console.error('I/O task failed:', err.message);
        } else {
            console.log(`I/O task completed in ${end - start}ms, read ${data.length} characters`);
        }
    });
}

// Execute both types
setTimeout(() => {
    cpuIntensiveTask();
    ioIntensiveTask();
}, 100);

// 8. Event Loop Monitoring
console.log('\n8. EVENT LOOP MONITORING');

// Monitor event loop lag
function measureEventLoopLag() {
    const start = process.hrtime.bigint();
    
    setImmediate(() => {
        const lag = process.hrtime.bigint() - start;
        console.log(`Event loop lag: ${Number(lag) / 1000000}ms`);
    });
}

// Measure lag multiple times
setTimeout(() => {
    measureEventLoopLag();
    setTimeout(() => measureEventLoopLag(), 10);
    setTimeout(() => measureEventLoopLag(), 20);
}, 200);

// 9. Graceful Shutdown Example
console.log('\n9. GRACEFUL SHUTDOWN DEMO');

// Handle process termination
process.on('SIGINT', () => {
    console.log('\nReceived SIGINT, performing graceful shutdown...');
    
    // Cleanup operations
    setTimeout(() => {
        console.log('Cleanup completed, exiting...');
        process.exit(0);
    }, 100);
});

process.on('SIGTERM', () => {
    console.log('Received SIGTERM, shutting down gracefully...');
    process.exit(0);
});

// 10. Event Loop Best Practices Demo
console.log('\n10. BEST PRACTICES DEMO');

// Good: Non-blocking I/O
function goodAsyncPattern() {
    console.log('Good: Using async I/O');
    fs.readFile(__filename, 'utf8', (err, data) => {
        if (err) {
            console.error('Async read error:', err.message);
            return;
        }
        console.log('Async read success: file has', data.split('\n').length, 'lines');
    });
}

// Bad: Blocking I/O (for demonstration only)
function badSyncPattern() {
    console.log('Bad: Using sync I/O (blocks event loop)');
    try {
        const data = fs.readFileSync(__filename, 'utf8');
        console.log('Sync read success: file has', data.split('\n').length, 'lines');
    } catch (err) {
        console.error('Sync read error:', err.message);
    }
}

// Demonstrate the difference
setTimeout(() => {
    console.log('Before I/O operations');
    goodAsyncPattern();
    badSyncPattern();
    console.log('After I/O operations');
}, 300);

// Final message
setTimeout(() => {
    console.log('\n=== Event Loop Demo Complete ===');
    console.log('Key Takeaways:');
    console.log('1. Event loop processes operations in phases');
    console.log('2. Microtasks (nextTick, Promises) have higher priority');
    console.log('3. Use async I/O to keep event loop responsive');
    console.log('4. CPU-intensive tasks can block the event loop');
    console.log('5. Understanding execution order helps debug async code');
}, 500);

// Export for testing
module.exports = {
    measureEventLoopLag,
    cpuIntensiveTask,
    ioIntensiveTask
};
