// Week 1 Exercise: Basic Calculator
// Date: [Enter today's date]
// Student: [Enter your name]

console.log("=== Basic Calculator Exercise ===");

/*
EXERCISE INSTRUCTIONS:
Create a basic calculator that demonstrates:
1. All arithmetic operators (+, -, *, /, %, **)
2. Proper error handling (division by zero)
3. Input validation
4. Different number formats (integers, decimals, negative numbers)
5. Order of operations understanding

Complete each section below by writing the required code.
*/

// ===== SECTION 1: BASIC ARITHMETIC FUNCTIONS =====
console.log("\n1. Basic Arithmetic Functions:");

// TODO: Create functions for each arithmetic operation
// Each function should take two parameters and return the result

function add(a, b) {
    // Your code here
    return a + b;
}

function subtract(a, b) {
    // Your code here
    return a - b;
}

function multiply(a, b) {
    // Your code here
    return a * b;
}

function divide(a, b) {
    // Your code here - handle division by zero
    if (b === 0) {
        return "Error: Cannot divide by zero";
    }
    return a / b;
}

function modulus(a, b) {
    // Your code here - handle modulus by zero
    if (b === 0) {
        return "Error: Cannot calculate modulus by zero";
    }
    return a % b;
}

function power(a, b) {
    // Your code here
    return a ** b;
}

// Test your functions
console.log("Testing basic functions:");
console.log(`add(10, 5) = ${add(10, 5)}`);
console.log(`subtract(10, 5) = ${subtract(10, 5)}`);
console.log(`multiply(10, 5) = ${multiply(10, 5)}`);
console.log(`divide(10, 5) = ${divide(10, 5)}`);
console.log(`divide(10, 0) = ${divide(10, 0)}`);
console.log(`modulus(10, 3) = ${modulus(10, 3)}`);
console.log(`power(2, 3) = ${power(2, 3)}`);

// ===== SECTION 2: INPUT VALIDATION =====
console.log("\n2. Input Validation:");

// TODO: Create a function to validate if input is a valid number
function isValidNumber(input) {
    // Your code here
    // Should return true if input is a valid number, false otherwise
    // Handle: strings, null, undefined, NaN, Infinity
    
    if (input === null || input === undefined) {
        return false;
    }
    
    if (typeof input === 'string' && input.trim() === '') {
        return false;
    }
    
    let num = Number(input);
    return !isNaN(num) && isFinite(num);
}

// TODO: Create a function to convert input to number safely
function safeNumberConversion(input) {
    // Your code here
    // Should convert input to number if valid, otherwise return null
    
    if (!isValidNumber(input)) {
        return null;
    }
    
    return Number(input);
}

// Test validation functions
console.log("Testing validation:");
console.log(`isValidNumber(42): ${isValidNumber(42)}`);
console.log(`isValidNumber("42"): ${isValidNumber("42")}`);
console.log(`isValidNumber("abc"): ${isValidNumber("abc")}`);
console.log(`isValidNumber(null): ${isValidNumber(null)}`);
console.log(`isValidNumber(""): ${isValidNumber("")}`);

console.log(`safeNumberConversion("123"): ${safeNumberConversion("123")}`);
console.log(`safeNumberConversion("abc"): ${safeNumberConversion("abc")}`);

// ===== SECTION 3: COMPREHENSIVE CALCULATOR FUNCTION =====
console.log("\n3. Comprehensive Calculator:");

// TODO: Create a main calculator function that:
// - Takes two numbers and an operator
// - Validates inputs
// - Performs the calculation
// - Returns result with proper error handling

function calculator(num1, operator, num2) {
    // Your code here
    
    // Validate numbers
    let n1 = safeNumberConversion(num1);
    let n2 = safeNumberConversion(num2);
    
    if (n1 === null) {
        return `Error: First number "${num1}" is not valid`;
    }
    
    if (n2 === null) {
        return `Error: Second number "${num2}" is not valid`;
    }
    
    // Validate operator
    let validOperators = ['+', '-', '*', '/', '%', '**'];
    if (!validOperators.includes(operator)) {
        return `Error: Invalid operator "${operator}". Use: ${validOperators.join(', ')}`;
    }
    
    // Perform calculation
    let result;
    switch (operator) {
        case '+':
            result = add(n1, n2);
            break;
        case '-':
            result = subtract(n1, n2);
            break;
        case '*':
            result = multiply(n1, n2);
            break;
        case '/':
            result = divide(n1, n2);
            break;
        case '%':
            result = modulus(n1, n2);
            break;
        case '**':
            result = power(n1, n2);
            break;
    }
    
    return result;
}

// Test the comprehensive calculator
console.log("Testing comprehensive calculator:");
console.log(`calculator(10, "+", 5): ${calculator(10, "+", 5)}`);
console.log(`calculator("15", "-", "3"): ${calculator("15", "-", "3")}`);
console.log(`calculator(8, "*", 4): ${calculator(8, "*", 4)}`);
console.log(`calculator(20, "/", 4): ${calculator(20, "/", 4)}`);
console.log(`calculator(17, "%", 5): ${calculator(17, "%", 5)}`);
console.log(`calculator(2, "**", 8): ${calculator(2, "**", 8)}`);

// Test error cases
console.log("\nTesting error cases:");
console.log(`calculator(10, "/", 0): ${calculator(10, "/", 0)}`);
console.log(`calculator("abc", "+", 5): ${calculator("abc", "+", 5)}`);
console.log(`calculator(10, "&", 5): ${calculator(10, "&", 5)}`);

// ===== SECTION 4: ADVANCED CALCULATOR FEATURES =====
console.log("\n4. Advanced Features:");

// TODO: Create a function for percentage calculations
function calculatePercentage(value, percentage) {
    // Your code here
    // Calculate what percentage of value is the given percentage
    // Example: calculatePercentage(200, 15) should return 30 (15% of 200)
    
    let val = safeNumberConversion(value);
    let pct = safeNumberConversion(percentage);
    
    if (val === null || pct === null) {
        return "Error: Invalid input for percentage calculation";
    }
    
    return (val * pct) / 100;
}

// TODO: Create a function to calculate compound operations
function calculateExpression(num1, op1, num2, op2, num3) {
    // Your code here
    // Calculate expressions like: num1 op1 num2 op2 num3
    // Follow order of operations (PEMDAS/BODMAS)
    // Example: calculateExpression(2, "+", 3, "*", 4) should return 14 (not 20)
    
    let n1 = safeNumberConversion(num1);
    let n2 = safeNumberConversion(num2);
    let n3 = safeNumberConversion(num3);
    
    if (n1 === null || n2 === null || n3 === null) {
        return "Error: Invalid numbers in expression";
    }
    
    // Handle order of operations
    let highPriorityOps = ['*', '/', '%', '**'];
    let result;
    
    if (highPriorityOps.includes(op2)) {
        // Do second operation first
        let secondResult = calculator(n2, op2, n3);
        if (typeof secondResult === 'string' && secondResult.includes('Error')) {
            return secondResult;
        }
        result = calculator(n1, op1, secondResult);
    } else if (highPriorityOps.includes(op1)) {
        // Do first operation first
        let firstResult = calculator(n1, op1, n2);
        if (typeof firstResult === 'string' && firstResult.includes('Error')) {
            return firstResult;
        }
        result = calculator(firstResult, op2, n3);
    } else {
        // Same priority, left to right
        let firstResult = calculator(n1, op1, n2);
        if (typeof firstResult === 'string' && firstResult.includes('Error')) {
            return firstResult;
        }
        result = calculator(firstResult, op2, n3);
    }
    
    return result;
}

// Test advanced features
console.log("Testing percentage calculation:");
console.log(`15% of 200: ${calculatePercentage(200, 15)}`);
console.log(`25% of 80: ${calculatePercentage(80, 25)}`);

console.log("Testing compound expressions:");
console.log(`2 + 3 * 4 = ${calculateExpression(2, "+", 3, "*", 4)}`); // Should be 14
console.log(`10 - 6 / 2 = ${calculateExpression(10, "-", 6, "/", 2)}`); // Should be 7
console.log(`5 * 2 + 3 = ${calculateExpression(5, "*", 2, "+", 3)}`); // Should be 13

// ===== SECTION 5: CALCULATOR HISTORY =====
console.log("\n5. Calculator History:");

// TODO: Create a calculator with history tracking
let calculatorHistory = [];

function calculatorWithHistory(num1, operator, num2) {
    // Your code here
    // Use the calculator function and store the operation in history
    
    let result = calculator(num1, operator, num2);
    
    let operation = {
        num1: num1,
        operator: operator,
        num2: num2,
        result: result,
        timestamp: new Date().toLocaleString()
    };
    
    calculatorHistory.push(operation);
    
    return result;
}

function showHistory() {
    // Your code here
    // Display all calculations from history
    
    console.log("Calculator History:");
    if (calculatorHistory.length === 0) {
        console.log("No calculations yet.");
        return;
    }
    
    calculatorHistory.forEach((operation, index) => {
        console.log(`${index + 1}. ${operation.num1} ${operation.operator} ${operation.num2} = ${operation.result} (${operation.timestamp})`);
    });
}

function clearHistory() {
    // Your code here
    calculatorHistory = [];
    console.log("History cleared.");
}

// Test calculator with history
console.log("Testing calculator with history:");
calculatorWithHistory(10, "+", 5);
calculatorWithHistory(20, "*", 3);
calculatorWithHistory(100, "/", 4);
showHistory();

// ===== BONUS CHALLENGES =====
console.log("\n=== Bonus Challenges ===");

// BONUS 1: Create a function to handle multiple operations in sequence
function chainCalculations(startValue, operations) {
    // Your code here
    // operations should be an array of [operator, number] pairs
    // Example: chainCalculations(10, [["+", 5], ["*", 2], ["/", 3]])
    
    let result = safeNumberConversion(startValue);
    if (result === null) {
        return "Error: Invalid starting value";
    }
    
    for (let [operator, number] of operations) {
        result = calculator(result, operator, number);
        if (typeof result === 'string' && result.includes('Error')) {
            return result;
        }
    }
    
    return result;
}

console.log("Testing chain calculations:");
let operations = [["+", 5], ["*", 2], ["/", 3]];
console.log(`Starting with 10: ${chainCalculations(10, operations)}`);

// BONUS 2: Create a function to solve simple equations
function solveForX(a, operator, x_coefficient, equals, result) {
    // Your code here
    // Solve equations like: a + 2x = result (find x)
    // This is a simplified version - you can extend it
    
    console.log("Equation solver is a bonus challenge - implement if you want extra practice!");
    return "Not implemented yet";
}

// BONUS 3: Create a scientific calculator function
function scientificCalculator(operation, ...args) {
    // Your code here
    // Handle operations like: sin, cos, tan, sqrt, log, etc.
    // Use Math object methods
    
    switch(operation.toLowerCase()) {
        case 'sqrt':
            return args[0] >= 0 ? Math.sqrt(args[0]) : "Error: Cannot calculate square root of negative number";
        case 'sin':
            return Math.sin(args[0]);
        case 'cos':
            return Math.cos(args[0]);
        case 'tan':
            return Math.tan(args[0]);
        case 'log':
            return args[0] > 0 ? Math.log(args[0]) : "Error: Cannot calculate log of non-positive number";
        default:
            return "Error: Unknown scientific operation";
    }
}

console.log("Testing scientific calculator:");
console.log(`sqrt(16): ${scientificCalculator('sqrt', 16)}`);
console.log(`sin(0): ${scientificCalculator('sin', 0)}`);

console.log("\n=== Basic Calculator Exercise Complete ===");

/*
SELF-CHECK QUESTIONS:
1. Do all your arithmetic functions work correctly?
2. Does your input validation catch invalid inputs?
3. Does your calculator handle division by zero?
4. Does your expression calculator follow order of operations?
5. Does your history feature work properly?
6. Can you explain how each part of your code works?

NEXT STEPS:
1. Test your calculator with edge cases
2. Add more advanced features
3. Create a user interface (console-based menu)
4. Add more scientific functions
*/
