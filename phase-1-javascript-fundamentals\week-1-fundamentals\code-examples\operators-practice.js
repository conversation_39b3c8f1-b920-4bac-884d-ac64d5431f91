// Week 1 Day 3: Operators Practice Examples
// Date: [Enter today's date]

console.log("=== JavaScript Operators Practice ===");

// ===== ARITHMETIC OPERATORS =====
console.log("\n1. Arithmetic Operators:");

let a = 15;
let b = 4;

console.log(`a = ${a}, b = ${b}`);
console.log(`a + b = ${a + b}`); // Addition: 19
console.log(`a - b = ${a - b}`); // Subtraction: 11
console.log(`a * b = ${a * b}`); // Multiplication: 60
console.log(`a / b = ${a / b}`); // Division: 3.75
console.log(`a % b = ${a % b}`); // Modulus: 3
console.log(`a ** b = ${a ** b}`); // Exponentiation: 50625

// Order of operations (PEMDAS/BODMAS)
console.log("\nOrder of Operations:");
let result1 = 2 + 3 * 4; // 14 (not 20)
let result2 = (2 + 3) * 4; // 20
console.log(`2 + 3 * 4 = ${result1}`);
console.log(`(2 + 3) * 4 = ${result2}`);

// ===== ASSIGNMENT OPERATORS =====
console.log("\n2. Assignment Operators:");

let x = 10;
console.log(`Initial x = ${x}`);

x += 5; // x = x + 5
console.log(`After x += 5: ${x}`);

x -= 3; // x = x - 3
console.log(`After x -= 3: ${x}`);

x *= 2; // x = x * 2
console.log(`After x *= 2: ${x}`);

x /= 4; // x = x / 4
console.log(`After x /= 4: ${x}`);

x %= 3; // x = x % 3
console.log(`After x %= 3: ${x}`);

// ===== COMPARISON OPERATORS =====
console.log("\n3. Comparison Operators:");

let num1 = 5;
let num2 = "5";
let num3 = 10;

console.log(`num1 = ${num1} (${typeof num1})`);
console.log(`num2 = ${num2} (${typeof num2})`);
console.log(`num3 = ${num3} (${typeof num3})`);

// Equality comparisons
console.log(`num1 == num2: ${num1 == num2}`); // true (loose equality)
console.log(`num1 === num2: ${num1 === num2}`); // false (strict equality)
console.log(`num1 != num2: ${num1 != num2}`); // false
console.log(`num1 !== num2: ${num1 !== num2}`); // true

// Relational comparisons
console.log(`num1 < num3: ${num1 < num3}`); // true
console.log(`num1 > num3: ${num1 > num3}`); // false
console.log(`num1 <= num3: ${num1 <= num3}`); // true
console.log(`num1 >= num3: ${num1 >= num3}`); // false

// ===== LOGICAL OPERATORS =====
console.log("\n4. Logical Operators:");

let isLoggedIn = true;
let hasPermission = false;
let isAdmin = true;

console.log(`isLoggedIn = ${isLoggedIn}`);
console.log(`hasPermission = ${hasPermission}`);
console.log(`isAdmin = ${isAdmin}`);

// AND operator (&&)
console.log(`isLoggedIn && hasPermission: ${isLoggedIn && hasPermission}`); // false
console.log(`isLoggedIn && isAdmin: ${isLoggedIn && isAdmin}`); // true

// OR operator (||)
console.log(`hasPermission || isAdmin: ${hasPermission || isAdmin}`); // true
console.log(`hasPermission || false: ${hasPermission || false}`); // false

// NOT operator (!)
console.log(`!isLoggedIn: ${!isLoggedIn}`); // false
console.log(`!hasPermission: ${!hasPermission}`); // true

// Complex logical expressions
let canAccess = isLoggedIn && (hasPermission || isAdmin);
console.log(`Can access (logged in AND (has permission OR is admin)): ${canAccess}`);

// ===== TERNARY OPERATOR =====
console.log("\n5. Ternary Operator:");

let age = 20;
let status = age >= 18 ? "adult" : "minor";
console.log(`Age: ${age}, Status: ${status}`);

let score = 85;
let grade = score >= 90 ? "A" : score >= 80 ? "B" : score >= 70 ? "C" : "F";
console.log(`Score: ${score}, Grade: ${grade}`);

// ===== UNARY OPERATORS =====
console.log("\n6. Unary Operators:");

let count = 5;
console.log(`Initial count: ${count}`);

// Pre-increment and post-increment
console.log(`count++: ${count++}`); // Returns 5, then increments to 6
console.log(`After post-increment: ${count}`); // 6
console.log(`++count: ${++count}`); // Increments to 7, then returns 7

// Pre-decrement and post-decrement
console.log(`count--: ${count--}`); // Returns 7, then decrements to 6
console.log(`After post-decrement: ${count}`); // 6
console.log(`--count: ${--count}`); // Decrements to 5, then returns 5

// Unary plus and minus
let str = "42";
console.log(`+str: ${+str} (${typeof +str})`); // Converts to number
console.log(`-str: ${-str} (${typeof -str})`); // Converts to negative number

// ===== TYPEOF OPERATOR =====
console.log("\n7. Typeof Operator:");

console.log(`typeof 42: ${typeof 42}`);
console.log(`typeof "hello": ${typeof "hello"}`);
console.log(`typeof true: ${typeof true}`);
console.log(`typeof undefined: ${typeof undefined}`);
console.log(`typeof null: ${typeof null}`); // Returns "object" - known quirk!
console.log(`typeof {}: ${typeof {}}`);
console.log(`typeof []: ${typeof []}`);
console.log(`typeof function(){}: ${typeof function(){}}`);

// ===== NULLISH COALESCING OPERATOR (ES2020) =====
console.log("\n8. Nullish Coalescing Operator (??):");

let username = null;
let defaultName = username ?? "Guest";
console.log(`Username: ${username}, Default: ${defaultName}`);

let userAge = 0;
let displayAge = userAge ?? 18;
console.log(`User age: ${userAge}, Display age: ${displayAge}`);

// Difference between || and ??
let value1 = "" || "default"; // "default" (empty string is falsy)
let value2 = "" ?? "default"; // "" (empty string is not null/undefined)
console.log(`"" || "default": ${value1}`);
console.log(`"" ?? "default": ${value2}`);

// ===== PRACTICAL EXAMPLES =====
console.log("\n9. Practical Examples:");

// Age verification system
function checkAge(age) {
    let canVote = age >= 18;
    let canDrink = age >= 21;
    let category = age < 13 ? "child" : age < 18 ? "teenager" : "adult";
    
    console.log(`Age: ${age}`);
    console.log(`Can vote: ${canVote}`);
    console.log(`Can drink: ${canDrink}`);
    console.log(`Category: ${category}`);
}

checkAge(16);
checkAge(25);

// Simple calculator
function calculate(num1, operator, num2) {
    let result;
    
    switch(operator) {
        case '+':
            result = num1 + num2;
            break;
        case '-':
            result = num1 - num2;
            break;
        case '*':
            result = num1 * num2;
            break;
        case '/':
            result = num2 !== 0 ? num1 / num2 : "Cannot divide by zero";
            break;
        case '%':
            result = num2 !== 0 ? num1 % num2 : "Cannot divide by zero";
            break;
        default:
            result = "Invalid operator";
    }
    
    console.log(`${num1} ${operator} ${num2} = ${result}`);
    return result;
}

console.log("\nCalculator Examples:");
calculate(10, '+', 5);
calculate(10, '-', 3);
calculate(10, '*', 4);
calculate(10, '/', 2);
calculate(10, '%', 3);
calculate(10, '/', 0);

console.log("\n=== Operators Practice Complete ===");

// Practice exercises for you to try:
console.log("\n=== Practice Exercises ===");
console.log("Try these exercises:");
console.log("1. Create a function that uses all comparison operators");
console.log("2. Build a grade calculator using ternary operators");
console.log("3. Practice operator precedence with complex expressions");
console.log("4. Create a user permission system using logical operators");
