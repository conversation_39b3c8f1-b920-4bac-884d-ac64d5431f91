# Phase 1: JavaScript Fundamentals - Detailed Guide

## Overview
Phase 1 is your foundation for becoming a backend developer. You'll master core JavaScript concepts that are essential for Node.js development. This phase typically takes 4-6 weeks of consistent study (2-3 hours daily).

**Learning Objectives:**
- Master JavaScript syntax and core concepts
- Understand asynchronous programming patterns
- Write clean, maintainable JavaScript code
- Handle errors effectively
- Use modern ES6+ features confidently

---

## Week 1: Variables, Data Types & Basic Operations

### Day 1-2: Variables and Data Types

#### Variable Declarations
```javascript
// var - function-scoped, can be redeclared and updated
var name = "<PERSON>";
var name = "<PERSON>"; // This works but is not recommended

// let - block-scoped, can be updated but not redeclared
let age = 25;
age = 26; // This works
// let age = 27; // This would cause an error

// const - block-scoped, cannot be updated or redeclared
const PI = 3.14159;
// PI = 3.14; // This would cause an error
```

#### Primitive Data Types
```javascript
// String
let firstName = "John";
let lastName = 'Doe';
let fullName = `${firstName} ${lastName}`; // Template literal

// Number
let integer = 42;
let decimal = 3.14;
let negative = -10;
let infinity = Infinity;
let notANumber = NaN;

// Boolean
let isActive = true;
let isCompleted = false;

// Undefined
let undefinedVariable;
console.log(undefinedVariable); // undefined

// Null
let emptyValue = null;

// Symbol (ES6)
let sym1 = Symbol('id');
let sym2 = Symbol('id');
console.log(sym1 === sym2); // false

// BigInt (ES2020)
let bigNumber = 1234567890123456789012345678901234567890n;
```

#### Reference Types
```javascript
// Objects
let person = {
    name: "John",
    age: 30,
    isEmployed: true,
    address: {
        street: "123 Main St",
        city: "New York"
    }
};

// Arrays
let numbers = [1, 2, 3, 4, 5];
let mixedArray = [1, "hello", true, null, {name: "John"}];

// Functions
function greet(name) {
    return `Hello, ${name}!`;
}
```

#### Type Checking and Conversion
```javascript
// Type checking
console.log(typeof "hello"); // "string"
console.log(typeof 42); // "number"
console.log(typeof true); // "boolean"
console.log(typeof undefined); // "undefined"
console.log(typeof null); // "object" (this is a known quirk)
console.log(typeof {}); // "object"
console.log(typeof []); // "object"
console.log(Array.isArray([])); // true

// Type conversion
let num = "123";
console.log(Number(num)); // 123
console.log(parseInt(num)); // 123
console.log(parseFloat("123.45")); // 123.45
console.log(String(123)); // "123"
console.log(Boolean(1)); // true
console.log(Boolean(0)); // false
```

#### Practical Exercise Day 1-2
Create a personal information manager that:
1. Stores different types of data about a person
2. Validates data types
3. Converts between different types when needed

### Day 3-4: Operators and Control Structures

#### Operators
```javascript
// Arithmetic operators
let a = 10, b = 3;
console.log(a + b); // Addition: 13
console.log(a - b); // Subtraction: 7
console.log(a * b); // Multiplication: 30
console.log(a / b); // Division: 3.333...
console.log(a % b); // Modulus: 1
console.log(a ** b); // Exponentiation: 1000

// Assignment operators
let x = 5;
x += 3; // x = x + 3; Result: 8
x -= 2; // x = x - 2; Result: 6
x *= 2; // x = x * 2; Result: 12
x /= 3; // x = x / 3; Result: 4

// Comparison operators
console.log(5 == "5"); // true (loose equality)
console.log(5 === "5"); // false (strict equality)
console.log(5 != "5"); // false
console.log(5 !== "5"); // true
console.log(5 > 3); // true
console.log(5 >= 5); // true

// Logical operators
let isLoggedIn = true;
let hasPermission = false;
console.log(isLoggedIn && hasPermission); // false (AND)
console.log(isLoggedIn || hasPermission); // true (OR)
console.log(!isLoggedIn); // false (NOT)

// Ternary operator
let age = 18;
let status = age >= 18 ? "adult" : "minor";
console.log(status); // "adult"

// Nullish coalescing operator (ES2020)
let username = null;
let defaultName = username ?? "Guest";
console.log(defaultName); // "Guest"
```

#### Control Structures
```javascript
// If-else statements
let score = 85;
if (score >= 90) {
    console.log("A grade");
} else if (score >= 80) {
    console.log("B grade");
} else if (score >= 70) {
    console.log("C grade");
} else {
    console.log("Need improvement");
}

// Switch statement
let day = "Monday";
switch (day) {
    case "Monday":
        console.log("Start of work week");
        break;
    case "Friday":
        console.log("TGIF!");
        break;
    case "Saturday":
    case "Sunday":
        console.log("Weekend!");
        break;
    default:
        console.log("Regular day");
}

// Loops
// For loop
for (let i = 0; i < 5; i++) {
    console.log(`Iteration ${i}`);
}

// While loop
let count = 0;
while (count < 3) {
    console.log(`Count: ${count}`);
    count++;
}

// Do-while loop
let num = 0;
do {
    console.log(`Number: ${num}`);
    num++;
} while (num < 3);

// For-of loop (for arrays)
let fruits = ["apple", "banana", "orange"];
for (let fruit of fruits) {
    console.log(fruit);
}

// For-in loop (for objects)
let person = {name: "John", age: 30, city: "New York"};
for (let key in person) {
    console.log(`${key}: ${person[key]}`);
}
```

#### Practical Exercise Day 3-4
Build a simple grade calculator that:
1. Takes multiple test scores
2. Calculates average
3. Assigns letter grades
4. Uses various operators and control structures

---

## Week 2: Functions and Scope

### Day 5-7: Functions Deep Dive

#### Function Declarations vs Expressions
```javascript
// Function Declaration - Hoisted
console.log(add(2, 3)); // This works due to hoisting
function add(a, b) {
    return a + b;
}

// Function Expression - Not hoisted
// console.log(subtract(5, 2)); // This would cause an error
const subtract = function(a, b) {
    return a - b;
};

// Arrow Functions (ES6)
const multiply = (a, b) => a * b;
const divide = (a, b) => {
    if (b === 0) {
        throw new Error("Division by zero");
    }
    return a / b;
};

// Single parameter arrow function
const square = x => x * x;

// No parameters
const greet = () => "Hello!";
```

#### Function Parameters and Arguments
```javascript
// Default parameters
function createUser(name, age = 18, role = "user") {
    return {
        name: name,
        age: age,
        role: role
    };
}

console.log(createUser("John")); // age=18, role="user"
console.log(createUser("Jane", 25, "admin"));

// Rest parameters
function sum(...numbers) {
    return numbers.reduce((total, num) => total + num, 0);
}

console.log(sum(1, 2, 3, 4, 5)); // 15

// Destructuring parameters
function displayUser({name, age, email}) {
    console.log(`Name: ${name}, Age: ${age}, Email: ${email}`);
}

displayUser({name: "John", age: 30, email: "<EMAIL>"});
```

#### Higher-Order Functions
```javascript
// Functions that take other functions as arguments
function processArray(arr, callback) {
    const result = [];
    for (let item of arr) {
        result.push(callback(item));
    }
    return result;
}

const numbers = [1, 2, 3, 4, 5];
const doubled = processArray(numbers, x => x * 2);
console.log(doubled); // [2, 4, 6, 8, 10]

// Functions that return other functions
function createMultiplier(factor) {
    return function(number) {
        return number * factor;
    };
}

const double = createMultiplier(2);
const triple = createMultiplier(3);
console.log(double(5)); // 10
console.log(triple(5)); // 15

// Array methods as higher-order functions
const fruits = ["apple", "banana", "cherry", "date"];

// map - transforms each element
const uppercased = fruits.map(fruit => fruit.toUpperCase());

// filter - selects elements based on condition
const longNames = fruits.filter(fruit => fruit.length > 5);

// reduce - combines all elements into single value
const totalLength = fruits.reduce((sum, fruit) => sum + fruit.length, 0);

// forEach - executes function for each element
fruits.forEach((fruit, index) => {
    console.log(`${index}: ${fruit}`);
});
```

### Day 8-10: Scope and Closures

#### Understanding Scope
```javascript
// Global scope
var globalVar = "I'm global";
let globalLet = "I'm also global";
const globalConst = "I'm global too";

function demonstrateScope() {
    // Function scope
    var functionVar = "I'm function-scoped";
    
    if (true) {
        // Block scope
        let blockLet = "I'm block-scoped";
        const blockConst = "I'm also block-scoped";
        var blockVar = "I'm function-scoped even in block";
        
        console.log(blockLet); // Works
        console.log(globalVar); // Works (accessing global)
    }
    
    // console.log(blockLet); // Error: blockLet is not defined
    console.log(blockVar); // Works (var is function-scoped)
}

// Scope chain example
let outerVariable = "outer";

function outerFunction() {
    let middleVariable = "middle";
    
    function innerFunction() {
        let innerVariable = "inner";
        console.log(innerVariable); // "inner"
        console.log(middleVariable); // "middle" (from outer scope)
        console.log(outerVariable); // "outer" (from global scope)
    }
    
    innerFunction();
}

outerFunction();
```

#### Closures
```javascript
// Basic closure example
function outerFunction(x) {
    // This is the outer function's scope
    
    function innerFunction(y) {
        // This inner function has access to the outer function's variables
        return x + y;
    }
    
    return innerFunction;
}

const addFive = outerFunction(5);
console.log(addFive(3)); // 8 (5 + 3)

// Practical closure example: Counter
function createCounter() {
    let count = 0;
    
    return {
        increment: function() {
            count++;
            return count;
        },
        decrement: function() {
            count--;
            return count;
        },
        getCount: function() {
            return count;
        }
    };
}

const counter = createCounter();
console.log(counter.increment()); // 1
console.log(counter.increment()); // 2
console.log(counter.getCount()); // 2
console.log(counter.decrement()); // 1

// Privacy with closures
function createBankAccount(initialBalance) {
    let balance = initialBalance;
    let transactions = [];
    
    return {
        deposit: function(amount) {
            if (amount > 0) {
                balance += amount;
                transactions.push({type: 'deposit', amount, balance});
                return balance;
            }
            throw new Error("Deposit amount must be positive");
        },
        
        withdraw: function(amount) {
            if (amount > 0 && amount <= balance) {
                balance -= amount;
                transactions.push({type: 'withdrawal', amount, balance});
                return balance;
            }
            throw new Error("Invalid withdrawal amount");
        },
        
        getBalance: function() {
            return balance;
        },
        
        getTransactionHistory: function() {
            return [...transactions]; // Return a copy
        }
    };
}

const account = createBankAccount(1000);
console.log(account.deposit(500)); // 1500
console.log(account.withdraw(200)); // 1300
console.log(account.getBalance()); // 1300
// console.log(balance); // Error: balance is not accessible
```

#### The `this` Keyword
```javascript
// Global context
console.log(this); // In browser: window object, in Node.js: global object

// Object method
const person = {
    name: "John",
    age: 30,
    greet: function() {
        console.log(`Hello, I'm ${this.name} and I'm ${this.age} years old`);
    },
    
    // Arrow function doesn't bind this
    greetArrow: () => {
        console.log(`Hello, I'm ${this.name}`); // this.name will be undefined
    }
};

person.greet(); // "Hello, I'm John and I'm 30 years old"

// Function context
function regularFunction() {
    console.log(this); // In strict mode: undefined, otherwise: global object
}

// Call, apply, and bind
function introduce(greeting, punctuation) {
    console.log(`${greeting}, I'm ${this.name}${punctuation}`);
}

const user = {name: "Alice"};

// call - invoke immediately with specific this
introduce.call(user, "Hi", "!");

// apply - invoke immediately with specific this, arguments as array
introduce.apply(user, ["Hello", "."]);

// bind - create new function with specific this
const boundIntroduce = introduce.bind(user);
boundIntroduce("Hey", "!!!");

// Constructor function context
function Person(name, age) {
    this.name = name;
    this.age = age;
    this.greet = function() {
        console.log(`Hi, I'm ${this.name}`);
    };
}

const john = new Person("John", 25);
john.greet(); // "Hi, I'm John"
```

#### Practical Exercise Week 2
Build a task management system using closures that:
1. Creates private task storage
2. Provides methods to add, remove, and list tasks
3. Tracks task completion status
4. Uses proper scope management

---

## Week 3: Asynchronous JavaScript

### Day 11-13: Callbacks and Promises

#### Understanding Asynchronous Behavior
```javascript
// Synchronous vs Asynchronous
console.log("Start");

// Synchronous operation
function syncOperation() {
    console.log("Synchronous operation");
}

// Asynchronous operation
setTimeout(() => {
    console.log("Asynchronous operation");
}, 0);

syncOperation();
console.log("End");

// Output:
// Start
// Synchronous operation
// End  
// Asynchronous operation
```

#### Callbacks
```javascript
// Basic callback example
function fetchData(callback) {
    setTimeout(() => {
        const data = {id: 1, name: "John", email: "<EMAIL>"};
        callback(null, data); // Error-first callback pattern
    }, 1000);
}

function handleData(error, data) {
    if (error) {
        console.error("Error:", error);
    } else {
        console.log("Received data:", data);
    }
}

fetchData(handleData);

// Callback hell example
function getUser(userId, callback) {
    setTimeout(() => {
        callback(null, {id: userId, name: "John"});
    }, 1000);
}

function getPosts(userId, callback) {
    setTimeout(() => {
        callback(null, [{id: 1, title: "Post 1"}, {id: 2, title: "Post 2"}]);
    }, 1000);
}

function getComments(postId, callback) {
    setTimeout(() => {
        callback(null, [{id: 1, text: "Great post!"}]);
    }, 1000);
}

// This creates callback hell
getUser(1, (err, user) => {
    if (err) throw err;
    console.log("User:", user);
    
    getPosts(user.id, (err, posts) => {
        if (err) throw err;
        console.log("Posts:", posts);
        
        getComments(posts[0].id, (err, comments) => {
            if (err) throw err;
            console.log("Comments:", comments);
        });
    });
});
```

#### Promises
```javascript
// Creating a Promise
function fetchUserData(userId) {
    return new Promise((resolve, reject) => {
        setTimeout(() => {
            if (userId > 0) {
                const userData = {id: userId, name: "John", email: "<EMAIL>"};
                resolve(userData);
            } else {
                reject(new Error("Invalid user ID"));
            }
        }, 1000);
    });
}

// Using Promises
fetchUserData(1)
    .then(user => {
        console.log("User data:", user);
        return user.id; // This value is passed to the next then
    })
    .then(userId => {
        console.log("User ID:", userId);
        return fetchUserPosts(userId); // Return another promise
    })
    .then(posts => {
        console.log("User posts:", posts);
    })
    .catch(error => {
        console.error("Error:", error.message);
    })
    .finally(() => {
        console.log("Operation completed");
    });

// Promise.all - Wait for all promises to resolve
function fetchUserPosts(userId) {
    return new Promise(resolve => {
        setTimeout(() => {
            resolve([{id: 1, title: "Post 1"}, {id: 2, title: "Post 2"}]);
        }, 500);
    });
}

function fetchUserProfile(userId) {
    return new Promise(resolve => {
        setTimeout(() => {
            resolve({bio: "Software developer", location: "New York"});
        }, 800);
    });
}

Promise.all([
    fetchUserData(1),
    fetchUserPosts(1),
    fetchUserProfile(1)
])
.then(([user, posts, profile]) => {
    console.log("All data loaded:", {user, posts, profile});
})
.catch(error => {
    console.error("One of the promises failed:", error);
});

// Promise.race - First promise to resolve/reject wins
Promise.race([
    fetchUserData(1),
    fetchUserPosts(1)
])
.then(result => {
    console.log("First result:", result);
});

// Promise.allSettled - Wait for all promises to settle (resolve or reject)
Promise.allSettled([
    fetchUserData(1),
    fetchUserData(-1), // This will reject
    fetchUserPosts(1)
])
.then(results => {
    results.forEach((result, index) => {
        if (result.status === 'fulfilled') {
            console.log(`Promise ${index} fulfilled:`, result.value);
        } else {
            console.log(`Promise ${index} rejected:`, result.reason);
        }
    });
});
```

### Day 14-16: Async/Await and Advanced Patterns

#### Async/Await Syntax
```javascript
// Converting Promise-based code to async/await
async function getUserData(userId) {
    try {
        const user = await fetchUserData(userId);
        console.log("User:", user);
        
        const posts = await fetchUserPosts(user.id);
        console.log("Posts:", posts);
        
        const profile = await fetchUserProfile(user.id);
        console.log("Profile:", profile);
        
        return {user, posts, profile};
    } catch (error) {
        console.error("Error fetching user data:", error.message);
        throw error; // Re-throw if you want calling code to handle it
    }
}

// Using the async function
getUserData(1)
    .then(allData => {
        console.log("All user data:", allData);
    })
    .catch(error => {
        console.error("Failed to get user data:", error);
    });

// Async/await with Promise.all
async function getAllUserData(userId) {
    try {
        const [user, posts, profile] = await Promise.all([
            fetchUserData(userId),
            fetchUserPosts(userId),
            fetchUserProfile(userId)
        ]);
        
        return {user, posts, profile};
    } catch (error) {
        console.error("Error fetching data:", error);
        throw error;
    }
}

// Sequential vs Parallel execution
async function sequentialExecution() {
    console.time("Sequential");
    
    const user1 = await fetchUserData(1); // Wait 1 second
    const user2 = await fetchUserData(2); // Wait another 1 second
    const user3 = await fetchUserData(3); // Wait another 1 second
    
    console.timeEnd("Sequential"); // ~3 seconds
    return [user1, user2, user3];
}

async function parallelExecution() {
    console.time("Parallel");
    
    const [user1, user2, user3] = await Promise.all([
        fetchUserData(1), // All execute simultaneously
        fetchUserData(2),
        fetchUserData(3)
    ]);
    
    console.timeEnd("Parallel"); // ~1 second
    return [user1, user2, user3];
}
```

#### Advanced Async Patterns
```javascript
// Async iteration
async function* asyncGenerator() {
    let i = 0;
    while (i < 3) {
        await new Promise(resolve => setTimeout(resolve, 1000));
        yield `Value ${i++}`;
    }
}

async function useAsyncGenerator() {
    for await (const value of asyncGenerator()) {
        console.log(value);
    }
}

// Retry mechanism with exponential backoff
async function retryWithBackoff(asyncFn, maxRetries = 3, baseDelay = 1000) {
    let lastError;
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
        try {
            return await asyncFn();
        } catch (error) {
            lastError = error;
            
            if (attempt === maxRetries) {
                throw new Error(`Failed after ${maxRetries + 1} attempts: ${error.message}`);
            }
            
            const delay = baseDelay * Math.pow(2, attempt);
            console.log(`Attempt ${attempt + 1} failed, retrying in ${delay}ms...`);
            await new Promise(resolve => setTimeout(resolve, delay));
        }
    }
}

// Usage example
async function unreliableApiCall() {
    if (Math.random() < 0.7) { // 70% chance of failure
        throw new Error("API call failed");
    }
    return "Success!";
}

retryWithBackoff(unreliableApiCall)
    .then(result => console.log("Final result:", result))
    .catch(error => console.error("All retries failed:", error.message));

// Timeout wrapper
function withTimeout(promise, timeoutMs) {
    const timeout = new Promise((_, reject) => {
        setTimeout(() => reject(new Error("Operation timed out")), timeoutMs);
    });
    
    return Promise.race([promise, timeout]);
}

// Usage
async function slowOperation() {
    await new Promise(resolve => setTimeout(resolve, 5000)); // 5 second delay
    return "Completed";
}

withTimeout(slowOperation(), 3000) // 3 second timeout
    .then(result => console.log(result))
    .catch(error => console.error(error.message)); // "Operation timed out"
```

#### Event Loop Understanding
```javascript
// Understanding the event loop
console.log("1: Start");

setTimeout(() => console.log("2: setTimeout 0"), 0);

Promise.resolve().then(() => console.log("3: Promise"));

setTimeout(() => console.log("4: setTimeout 10"), 10);

setImmediate(() => console.log("5: setImmediate")); // Node.js only

process.nextTick(() => console.log("6: nextTick")); // Node.js only

console.log("7: End");

// Output order (in Node.js):
// 1: Start
// 7: End
// 6: nextTick
// 3: Promise
// 5: setImmediate
// 2: setTimeout 0
// 4: setTimeout 10
```

#### Practical Exercise Week 3
Create an async data processing pipeline that:
1. Fetches data from multiple sources
2. Processes data asynchronously
3. Handles errors gracefully
4. Implements retry logic
5. Uses proper async/await patterns

---

## Week 4: ES6+ Features and Error Handling

### Day 17-19: Modern JavaScript Features

#### Destructuring Assignment
```javascript
// Array destructuring
const numbers = [1, 2, 3, 4, 5];
const [first, second, ...rest] = numbers;
console.log(first); // 1
console.log(second); // 2
console.log(rest); // [3, 4, 5]

// Skipping elements
const [a, , c] = numbers; // Skip second element
console.log(a, c); // 1, 3

// Default values
const [x, y, z = 0] = [1, 2];
console.log(x, y, z); // 1, 2, 0

// Object destructuring
const person = {
    name: "John",
    age: 30,
    address: {
        street: "123 Main St",
        city: "New York",
        country: "USA"
    },
    hobbies: ["reading", "swimming"]
};

const {name, age, email = "N/A"} = person;
console.log(name, age, email); // John, 30, N/A

// Renaming variables
const {name: fullName, age: years} = person;
console.log(fullName, years); // John, 30

// Nested destructuring
const {address: {city, country}} = person;
console.log(city, country); // New York, USA

// Function parameter destructuring
function displayUser({name, age, email = "Not provided"}) {
    console.log(`Name: ${name}, Age: ${age}, Email: ${email}`);
}

displayUser(person);

// Rest in object destructuring
const {name: userName, ...otherDetails} = person;
console.log(userName); // John
console.log(otherDetails); // {age: 30, address: {...}, hobbies: [...]}
```

#### Spread and Rest Operators
```javascript
// Spread operator with arrays
const arr1 = [1, 2, 3];
const arr2 = [4, 5, 6];
const combined = [...arr1, ...arr2];
console.log(combined); // [1, 2, 3, 4, 5, 6]

// Array cloning
const original = [1, 2, 3];
const copy = [...original];
copy.push(4);
console.log(original); // [1, 2, 3] (unchanged)
console.log(copy); // [1, 2, 3, 4]

// Spread with objects
const baseConfig = {
    host: "localhost",
    port: 3000,
    secure: false
};

const productionConfig = {
    ...baseConfig,
    host: "production.example.com",
    secure: true,
    logging: true
};

console.log(productionConfig);
// {host: "production.example.com", port: 3000, secure: true, logging: true}

// Rest parameters in functions
function sum(multiplier, ...numbers) {
    return numbers.reduce((total, num) => total + num, 0) * multiplier;
}

console.log(sum(2, 1, 2, 3, 4)); // 20 (10 * 2)

// Rest in array destructuring
const [head, ...tail] = [1, 2, 3, 4, 5];
console.log(head); // 1
console.log(tail); // [2, 3, 4, 5]
```

#### Template Literals and Tagged Templates
```javascript
// Basic template literals
const name = "John";
const age = 30;
const message = `Hello, my name is ${name} and I'm ${age} years old.`;

// Multi-line strings
const html = `
    <div class="user-card">
        <h2>${name}</h2>
        <p>Age: ${age}</p>
    </div>
`;

// Expressions in template literals
const price = 29.99;
const tax = 0.08;
const total = `Total: $${(price * (1 + tax)).toFixed(2)}`;

// Tagged template literals
function highlight(strings, ...values) {
    return strings.reduce((result, string, i) => {
        const value = values[i] ? `<mark>${values[i]}</mark>` : '';
        return result + string + value;
    }, '');
}

const searchTerm = "JavaScript";
const text = highlight`Learn ${searchTerm} for backend development`;
console.log(text); // "Learn <mark>JavaScript</mark> for backend development"

// Practical tagged template for SQL queries (basic example)
function sql(strings, ...values) {
    // In real applications, this would properly escape values
    return strings.reduce((query, string, i) => {
        const value = values[i] !== undefined ? `'${values[i]}'` : '';
        return query + string + value;
    }, '');
}

const userId = 123;
const query = sql`SELECT * FROM users WHERE id = ${userId}`;
console.log(query); // "SELECT * FROM users WHERE id = '123'"
```

#### Modules (Import/Export)
```javascript
// math.js - Named exports
export function add(a, b) {
    return a + b;
}

export function multiply(a, b) {
    return a * b;
}

export const PI = 3.14159;

// Alternative named export syntax
function subtract(a, b) {
    return a - b;
}

function divide(a, b) {
    if (b === 0) throw new Error("Division by zero");
    return a / b;
}

export { subtract, divide };

// Default export
export default function power(base, exponent) {
    return Math.pow(base, exponent);
}

// utils.js - Mixed exports
export const formatCurrency = (amount) => `${amount.toFixed(2)}`;

export class Calculator {
    constructor() {
        this.history = [];
    }
    
    calculate(operation, a, b) {
        let result;
        switch (operation) {
            case 'add': result = a + b; break;
            case 'subtract': result = a - b; break;
            case 'multiply': result = a * b; break;
            case 'divide': result = a / b; break;
            default: throw new Error('Unknown operation');
        }
        
        this.history.push({operation, a, b, result});
        return result;
    }
}

const config = {
    precision: 2,
    currency: 'USD'
};

export default config;

// app.js - Using imports
// Named imports
import { add, multiply, PI, subtract } from './math.js';
import { formatCurrency, Calculator } from './utils.js';

// Default import
import power from './math.js';
import config from './utils.js';

// Import with alias
import { divide as div } from './math.js';

// Import everything
import * as mathUtils from './math.js';

// Usage
console.log(add(5, 3)); // 8
console.log(power(2, 3)); // 8
console.log(formatCurrency(29.99)); // $29.99

const calc = new Calculator();
console.log(calc.calculate('multiply', 4, 5)); // 20

// Dynamic imports (ES2020)
async function loadMathModule() {
    try {
        const mathModule = await import('./math.js');
        console.log(mathModule.add(10, 5)); // 15
    } catch (error) {
        console.error('Failed to load module:', error);
    }
}
```

#### Classes and Inheritance
```javascript
// Basic class
class Person {
    // Private fields (ES2022)
    #socialSecurityNumber;
    
    constructor(name, age, ssn) {
        this.name = name;
        this.age = age;
        this.#socialSecurityNumber = ssn;
    }
    
    // Method
    introduce() {
        return `Hi, I'm ${this.name} and I'm ${this.age} years old.`;
    }
    
    // Getter
    get info() {
        return `${this.name} (${this.age})`;
    }
    
    // Setter  
    set age(value) {
        if (value < 0) {
            throw new Error("Age cannot be negative");
        }
        this._age = value;
    }
    
    get age() {
        return this._age;
    }
    
    // Private method
    #getSSN() {
        return this.#socialSecurityNumber;
    }
    
    // Static method
    static createAnonymous() {
        return new Person("Anonymous", 0, "***********");
    }
    
    // Static property
    static species = "Homo sapiens";
}

// Inheritance
class Employee extends Person {
    constructor(name, age, ssn, jobTitle, salary) {
        super(name, age, ssn); // Call parent constructor
        this.jobTitle = jobTitle;
        this.salary = salary;
    }
    
    // Override method
    introduce() {
        return `${super.introduce()} I work as a ${this.jobTitle}.`;
    }
    
    // New method
    getAnnualSalary() {
        return this.salary * 12;
    }
    
    // Static method
    static createIntern(name, age, ssn) {
        return new Employee(name, age, ssn, "Intern", 2000);
    }
}

// Usage
const person = new Person("John", 30, "***********");
console.log(person.introduce());
console.log(person.info);

const employee = new Employee("Jane", 28, "***********", "Developer", 5000);
console.log(employee.introduce());
console.log(employee.getAnnualSalary());

// Abstract-like patterns using classes
class Animal {
    constructor(name) {
        if (this.constructor === Animal) {
            throw new Error("Cannot instantiate abstract class");
        }
        this.name = name;
    }
    
    // "Abstract" method
    makeSound() {
        throw new Error("Must implement makeSound method");
    }
    
    move() {
        console.log(`${this.name} is moving`);
    }
}

class Dog extends Animal {
    makeSound() {
        return "Woof!";
    }
}

class Cat extends Animal {
    makeSound() {
        return "Meow!";
    }
}

const dog = new Dog("Rex");
console.log(dog.makeSound()); // "Woof!"
dog.move(); // "Rex is moving"
```

### Day 20-21: Error Handling

#### Try-Catch-Finally
```javascript
// Basic error handling
function divideNumbers(a, b) {
    try {
        if (typeof a !== 'number' || typeof b !== 'number') {
            throw new TypeError('Both arguments must be numbers');
        }
        
        if (b === 0) {
            throw new Error('Division by zero is not allowed');
        }
        
        const result = a / b;
        console.log(`${a} / ${b} = ${result}`);
        return result;
        
    } catch (error) {
        console.error('Error occurred:', error.message);
        
        // Handle different error types
        if (error instanceof TypeError) {
            console.error('Type error - check your input types');
        } else if (error instanceof Error) {
            console.error('General error - check your logic');
        }
        
        return null;
        
    } finally {
        console.log('Division operation completed');
    }
}

// Usage
divideNumbers(10, 2); // Works fine
divideNumbers(10, 0); // Throws Error
divideNumbers("10", 2); // Throws TypeError

// Async error handling
async function fetchUserData(userId) {
    try {
        // Simulate API call
        if (userId <= 0) {
            throw new Error('Invalid user ID');
        }
        
        // Simulate network delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        if (Math.random() < 0.3) { // 30% chance of network error
            throw new Error('Network error occurred');
        }
        
        return {
            id: userId,
            name: `User ${userId}`,
            email: `user${userId}@example.com`
        };
        
    } catch (error) {
        console.error('Failed to fetch user data:', error.message);
        throw error; // Re-throw for caller to handle
    }
}

async function handleUserData(userId) {
    try {
        const userData = await fetchUserData(userId);
        console.log('User data retrieved:', userData);
        return userData;
    } catch (error) {
        console.error('Error in handleUserData:', error.message);
        return null;
    }
}
```

#### Custom Error Classes
```javascript
// Custom error classes
class ValidationError extends Error {
    constructor(message, field) {
        super(message);
        this.name = 'ValidationError';
        this.field = field;
    }
}

class NetworkError extends Error {
    constructor(message, statusCode) {
        super(message);
        this.name = 'NetworkError';
        this.statusCode = statusCode;
    }
}

class DatabaseError extends Error {
    constructor(message, query) {
        super(message);
        this.name = 'DatabaseError';
        this.query = query;
    }
}

// User registration example with custom errors
class UserService {
    static users = []; // Simulate database
    
    static validateUser(userData) {
        if (!userData.name || userData.name.length < 2) {
            throw new ValidationError('Name must be at least 2 characters long', 'name');
        }
        
        if (!userData.email || !userData.email.includes('@')) {
            throw new ValidationError('Invalid email format', 'email');
        }
        
        if (!userData.password || userData.password.length < 8) {
            throw new ValidationError('Password must be at least 8 characters long', 'password');
        }
    }
    
    static async registerUser(userData) {
        try {
            // Validate input
            this.validateUser(userData);
            
            // Check if user already exists
            const existingUser = this.users.find(user => user.email === userData.email);
            if (existingUser) {
                throw new ValidationError('User with this email already exists', 'email');
            }
            
            // Simulate network operation
            await new Promise(resolve => setTimeout(resolve, 500));
            
            // Simulate potential database error
            if (Math.random() < 0.1) { // 10% chance of database error
                throw new DatabaseError('Failed to save user to database', 'INSERT INTO users...');
            }
            
            // Add user
            const newUser = {
                id: this.users.length + 1,
                ...userData,
                createdAt: new Date().toISOString()
            };
            
            this.users.push(newUser);
            console.log('User registered successfully:', newUser);
            return newUser;
            
        } catch (error) {
            // Log error details
            console.error('Registration failed:', {
                errorName: error.name,
                message: error.message,
                field: error.field,
                statusCode: error.statusCode,
                query: error.query
            });
            
            throw error; // Re-throw for caller
        }
    }
}

// Usage with comprehensive error handling
async function registerNewUser(userData) {
    try {
        const user = await UserService.registerUser(userData);
        return { success: true, user };
        
    } catch (error) {
        let errorResponse = { success: false };
        
        if (error instanceof ValidationError) {
            errorResponse.type = 'validation';
            errorResponse.field = error.field;
            errorResponse.message = error.message;
        } else if (error instanceof NetworkError) {
            errorResponse.type = 'network';
            errorResponse.statusCode = error.statusCode;
            errorResponse.message = 'Network error occurred';
        } else if (error instanceof DatabaseError) {
            errorResponse.type = 'database';
            errorResponse.message = 'Database error occurred';
        } else {
            errorResponse.type = 'unknown';
            errorResponse.message = 'An unexpected error occurred';
        }
        
        return errorResponse;
    }
}

// Test the error handling
async function testRegistration() {
    const testCases = [
        { name: "John", email: "<EMAIL>", password: "password123" }, // Valid
        { name: "A", email: "invalid-email", password: "123" }, // Multiple validation errors
        { name: "Jane", email: "<EMAIL>", password: "securepassword" } // Valid
    ];
    
    for (const userData of testCases) {
        console.log('\n--- Testing registration for:', userData.name);
        const result = await registerNewUser(userData);
        console.log('Result:', result);
    }
}

testRegistration();
```

#### Error Boundaries and Global Error Handling
```javascript
// Global error handling patterns
class ErrorHandler {
    static logError(error, context = {}) {
        const errorInfo = {
            message: error.message,
            name: error.name,
            stack: error.stack,
            timestamp: new Date().toISOString(),
            context
        };
        
        // In production, you'd send this to a logging service
        console.error('Error logged:', errorInfo);
        
        // Could also send to external service like Sentry
        // Sentry.captureException(error, { extra: context });
    }
    
    static handleAsyncError(asyncFn) {
        return async function wrappedAsyncFunction(...args) {
            try {
                return await asyncFn.apply(this, args);
            } catch (error) {
                ErrorHandler.logError(error, { 
                    function: asyncFn.name,
                    arguments: args
                });
                throw error;
            }
        };
    }
    
    static createErrorBoundary(fn) {
        return function errorBoundary(...args) {
            try {
                const result = fn.apply(this, args);
                
                // Handle promises returned by the function
                if (result && typeof result.catch === 'function') {
                    return result.catch(error => {
                        ErrorHandler.logError(error, { 
                            function: fn.name,
                            arguments: args
                        });
                        throw error;
                    });
                }
                
                return result;
            } catch (error) {
                ErrorHandler.logError(error, { 
                    function: fn.name,
                    arguments: args
                });
                throw error;
            }
        };
    }
}

// Usage examples
const safeAsyncFunction = ErrorHandler.handleAsyncError(async function fetchData(url) {
    // Simulate API call that might fail
    if (url.includes('invalid')) {
        throw new Error('Invalid URL provided');
    }
    return { data: 'Some data from ' + url };
});

// Process-level error handling (Node.js)
process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception:', error);
    ErrorHandler.logError(error, { type: 'uncaughtException' });
    
    // Graceful shutdown
    process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
    ErrorHandler.logError(new Error(reason), { 
        type: 'unhandledRejection',
        promise: promise.toString()
    });
});

// Graceful error handling with circuit breaker pattern
class CircuitBreaker {
    constructor(asyncFn, threshold = 5, timeout = 60000) {
        this.asyncFn = asyncFn;
        this.threshold = threshold;
        this.timeout = timeout;
        this.failureCount = 0;
        this.lastFailureTime = null;
        this.state = 'CLOSED'; // CLOSED, OPEN, HALF_OPEN
    }
    
    async execute(...args) {
        if (this.state === 'OPEN') {
            if (Date.now() - this.lastFailureTime > this.timeout) {
                this.state = 'HALF_OPEN';
            } else {
                throw new Error('Circuit breaker is OPEN');
            }
        }
        
        try {
            const result = await this.asyncFn(...args);
            this.onSuccess();
            return result;
        } catch (error) {
            this.onFailure();
            throw error;
        }
    }
    
    onSuccess() {
        this.failureCount = 0;
        this.state = 'CLOSED';
    }
    
    onFailure() {
        this.failureCount++;
        this.lastFailureTime = Date.now();
        
        if (this.failureCount >= this.threshold) {
            this.state = 'OPEN';
        }
    }
}

// Usage
const unreliableService = async (data) => {
    if (Math.random() < 0.7) {
        throw new Error('Service unavailable');
    }
    return `Processed: ${data}`;
};

const protectedService = new CircuitBreaker(unreliableService, 3, 5000);

async function testCircuitBreaker() {
    for (let i = 0; i < 10; i++) {
        try {
            const result = await protectedService.execute(`data-${i}`);
            console.log('Success:', result);
        } catch (error) {
            console.log('Failed:', error.message);
        }
        
        await new Promise(resolve => setTimeout(resolve, 500));
    }
}
```

#### Practical Exercise Week 4
Build a robust data processing application that:
1. Uses modern ES6+ features throughout
2. Implements comprehensive error handling
3. Creates custom error classes for different scenarios
4. Uses modules to organize code
5. Includes proper logging and error recovery mechanisms

---

## Final Project: JavaScript Fundamentals Showcase

### Project Requirements
Create a **Personal Task Management System** that demonstrates all Phase 1 concepts:

#### Core Features
1. **Task Management**
   - Create, read, update, delete tasks
   - Task categories and priorities
   - Due dates and reminders

2. **User System**
   - User registration and login simulation
   - User preferences and settings
   - Session management

3. **Data Persistence**
   - Save/load data from JSON files
   - Data validation and sanitization
   - Backup and restore functionality

4. **Advanced Features**
   - Search and filter tasks
   - Task statistics and reports
   - Import/export functionality

#### Technical Requirements
- Use ES6+ features (classes, modules, destructuring, etc.)
- Implement proper error handling with custom error classes
- Use async/await for all asynchronous operations
- Apply closures for data privacy
- Use higher-order functions for data processing
- Comprehensive input validation
- Proper code organization with modules

#### Project Structure
```
task-manager/
├── src/
│   ├── models/
│   │   ├── User.js
│   │   ├── Task.js
│   │   └── Category.js
│   ├── services/
│   │   ├── UserService.js
│   │   ├── TaskService.js
│   │   └── FileService.js
│   ├── utils/
│   │   ├── validators.js
│   │   ├── helpers.js
│   │   └── errors.js
│   └── app.js
├── data/
│   ├── users.json
│   └── tasks.json
├── tests/
│   └── basic-tests.js
└── README.md
```

#### Success Criteria
- Clean, readable, and well-documented code
- Proper error handling for all edge cases
- Modular architecture with clear separation of concerns
- Demonstration of all Phase 1 concepts
- Working application with all features functional

---

## Study Tips and Best Practices

### Daily Study Routine
1. **Theory (30 minutes)**: Read and understand concepts
2. **Practice (90 minutes)**: Write code and experiment
3. **Review (30 minutes)**: Go over what you learned and take notes

### Code Practice Guidelines
- Write code by hand first, then type it
- Experiment with variations of examples
- Break complex problems into smaller parts
- Use console.log() liberally to understand code flow
- Comment your code to explain your thinking

### Common Pitfalls to Avoid
- Don't rush through fundamentals
- Avoid copying code without understanding
- Don't skip error handling practice
- Avoid memorizing syntax without understanding concepts
- Don't move on until you can explain concepts to others

### Resources for Additional Practice
- **MDN Web Docs**: Comprehensive JavaScript reference
- **JavaScript.info**: Modern JavaScript tutorial
- **Eloquent JavaScript**: Free online book
- **freeCodeCamp**: Interactive exercises
- **Codewars**: Programming challenges

### Next Phase Preparation
By the end of Phase 1, you should be comfortable with:
- Writing clean, readable JavaScript code
- Understanding and using all major JavaScript features
- Handling asynchronous operations confidently
- Creating modular, organized code structures
- Debugging and error handling

You'll be ready to dive into Node.js and server-side development in Phase 2!

---

## Assessment Checklist

Before moving to Phase 2, ensure you can:

**Variables and Data Types**
- [ ] Explain the differences between var, let, and const
- [ ] Work with all primitive and reference data types
- [ ] Perform type checking and conversion

**Functions and Scope**
- [ ] Write different types of functions (declaration, expression, arrow)
- [ ] Understand and use closures effectively
- [ ] Explain how 'this' binding works
- [ ] Use higher-order functions confidently

**Asynchronous JavaScript**
- [ ] Handle callbacks and avoid callback hell
- [ ] Work with Promises and Promise methods
- [ ] Use async/await for clean asynchronous code
- [ ] Understand the event loop conceptually

**Modern JavaScript Features**
- [ ] Use destructuring assignment in various scenarios
- [ ] Apply spread and rest operators appropriately
- [ ] Create and use ES6 classes with inheritance
- [ ] Organize code with ES6 modules

**Error Handling**
- [ ] Implement try-catch-finally blocks effectively
- [ ] Create and use custom error classes
- [ ] Handle both synchronous and asynchronous errors
- [ ] Apply error handling best practices

**Final Project**
- [ ] Complete the Personal Task Management System
- [ ] Demonstrate all Phase 1 concepts in a working application
- [ ] Write clean, documented, and maintainable code

Once you can confidently check all these items, you're ready for Phase 2: Node.js Fundamentals!