// Week 1 Exercise: Grade Calculator
// Date: [Enter today's date]
// Student: [Enter your name]

console.log("=== Grade Calculator Exercise ===");

/*
EXERCISE INSTRUCTIONS:
Create a comprehensive grade calculator that demonstrates:
1. Arrays for storing multiple grades
2. Loops for processing data
3. Conditional statements for grade assignment
4. Functions for modular code
5. Input validation and error handling

Complete each section below by writing the required code.
*/

// ===== SECTION 1: BASIC GRADE FUNCTIONS =====
console.log("\n1. Basic Grade Functions:");

// TODO: Create a function to convert numeric score to letter grade
function getLetterGrade(score) {
    // Your code here
    // Use if-else or switch to assign letter grades
    // A: 90-100, B: 80-89, C: 70-79, D: 60-69, F: 0-59
    
    if (score < 0 || score > 100) {
        return "Invalid score";
    }
    
    if (score >= 90) {
        return "A";
    } else if (score >= 80) {
        return "B";
    } else if (score >= 70) {
        return "C";
    } else if (score >= 60) {
        return "D";
    } else {
        return "F";
    }
}

// TODO: Create a function to get grade point for GPA calculation
function getGradePoint(letterGrade) {
    // Your code here
    // A=4.0, B=3.0, C=2.0, D=1.0, F=0.0
    
    switch(letterGrade.toUpperCase()) {
        case 'A':
            return 4.0;
        case 'B':
            return 3.0;
        case 'C':
            return 2.0;
        case 'D':
            return 1.0;
        case 'F':
            return 0.0;
        default:
            return null;
    }
}

// TODO: Create a function to determine if grade is passing
function isPassingGrade(score) {
    // Your code here
    // Passing grade is 60 or above
    return score >= 60;
}

// Test basic functions
console.log("Testing basic grade functions:");
console.log(`Score 95 -> Grade: ${getLetterGrade(95)}`);
console.log(`Score 85 -> Grade: ${getLetterGrade(85)}`);
console.log(`Score 75 -> Grade: ${getLetterGrade(75)}`);
console.log(`Score 65 -> Grade: ${getLetterGrade(65)}`);
console.log(`Score 55 -> Grade: ${getLetterGrade(55)}`);

console.log(`Grade A -> Points: ${getGradePoint('A')}`);
console.log(`Grade C -> Points: ${getGradePoint('C')}`);

console.log(`Score 70 passing: ${isPassingGrade(70)}`);
console.log(`Score 55 passing: ${isPassingGrade(55)}`);

// ===== SECTION 2: GRADE STATISTICS =====
console.log("\n2. Grade Statistics:");

// TODO: Create a function to calculate average of grades
function calculateAverage(grades) {
    // Your code here
    // Handle empty array case
    
    if (!Array.isArray(grades) || grades.length === 0) {
        return 0;
    }
    
    let sum = 0;
    for (let grade of grades) {
        sum += grade;
    }
    
    return sum / grades.length;
}

// TODO: Create a function to find highest grade
function findHighestGrade(grades) {
    // Your code here
    
    if (!Array.isArray(grades) || grades.length === 0) {
        return null;
    }
    
    let highest = grades[0];
    for (let grade of grades) {
        if (grade > highest) {
            highest = grade;
        }
    }
    
    return highest;
}

// TODO: Create a function to find lowest grade
function findLowestGrade(grades) {
    // Your code here
    
    if (!Array.isArray(grades) || grades.length === 0) {
        return null;
    }
    
    let lowest = grades[0];
    for (let grade of grades) {
        if (grade < lowest) {
            lowest = grade;
        }
    }
    
    return lowest;
}

// TODO: Create a function to count passing grades
function countPassingGrades(grades) {
    // Your code here
    
    if (!Array.isArray(grades)) {
        return 0;
    }
    
    let count = 0;
    for (let grade of grades) {
        if (isPassingGrade(grade)) {
            count++;
        }
    }
    
    return count;
}

// Test statistics functions
let testGrades = [85, 92, 78, 96, 88, 73, 91, 84];
console.log("Test grades:", testGrades);
console.log(`Average: ${calculateAverage(testGrades).toFixed(2)}`);
console.log(`Highest: ${findHighestGrade(testGrades)}`);
console.log(`Lowest: ${findLowestGrade(testGrades)}`);
console.log(`Passing grades: ${countPassingGrades(testGrades)}/${testGrades.length}`);

// ===== SECTION 3: COMPREHENSIVE GRADE REPORT =====
console.log("\n3. Comprehensive Grade Report:");

// TODO: Create a function that generates a complete grade report
function generateGradeReport(studentName, grades) {
    // Your code here
    // Should return an object with all statistics and analysis
    
    if (!Array.isArray(grades) || grades.length === 0) {
        return {
            error: "No grades provided"
        };
    }
    
    let average = calculateAverage(grades);
    let letterGrade = getLetterGrade(average);
    let gradePoint = getGradePoint(letterGrade);
    let highest = findHighestGrade(grades);
    let lowest = findLowestGrade(grades);
    let passingCount = countPassingGrades(grades);
    let passingRate = (passingCount / grades.length) * 100;
    
    return {
        studentName: studentName,
        grades: [...grades], // Copy array
        totalGrades: grades.length,
        average: Math.round(average * 100) / 100, // Round to 2 decimal places
        letterGrade: letterGrade,
        gradePoint: gradePoint,
        highest: highest,
        lowest: lowest,
        passingCount: passingCount,
        failingCount: grades.length - passingCount,
        passingRate: Math.round(passingRate * 100) / 100,
        status: average >= 60 ? "PASSING" : "FAILING"
    };
}

// TODO: Create a function to display the grade report nicely
function displayGradeReport(report) {
    // Your code here
    // Format and display the report object
    
    if (report.error) {
        console.log(`Error: ${report.error}`);
        return;
    }
    
    console.log("=".repeat(40));
    console.log(`GRADE REPORT FOR: ${report.studentName.toUpperCase()}`);
    console.log("=".repeat(40));
    console.log(`Grades: ${report.grades.join(", ")}`);
    console.log(`Total Assignments: ${report.totalGrades}`);
    console.log(`Average Score: ${report.average}%`);
    console.log(`Letter Grade: ${report.letterGrade}`);
    console.log(`Grade Point: ${report.gradePoint}`);
    console.log(`Highest Score: ${report.highest}%`);
    console.log(`Lowest Score: ${report.lowest}%`);
    console.log(`Passing Assignments: ${report.passingCount}/${report.totalGrades}`);
    console.log(`Passing Rate: ${report.passingRate}%`);
    console.log(`Overall Status: ${report.status}`);
    console.log("=".repeat(40));
}

// Test comprehensive grade report
let johnGrades = [88, 92, 76, 84, 91, 79, 85, 90];
let johnReport = generateGradeReport("John Smith", johnGrades);
displayGradeReport(johnReport);

// ===== SECTION 4: MULTIPLE STUDENTS =====
console.log("\n4. Multiple Students Grade Management:");

// TODO: Create a class or object structure to manage multiple students
let classGrades = [];

function addStudent(name, grades) {
    // Your code here
    // Add a student with their grades to the class
    
    let report = generateGradeReport(name, grades);
    classGrades.push(report);
    return report;
}

function findStudent(name) {
    // Your code here
    // Find a student by name
    
    return classGrades.find(student => 
        student.studentName.toLowerCase() === name.toLowerCase()
    );
}

function getClassStatistics() {
    // Your code here
    // Calculate statistics for the entire class
    
    if (classGrades.length === 0) {
        return { error: "No students in class" };
    }
    
    let totalStudents = classGrades.length;
    let allAverages = classGrades.map(student => student.average);
    let classAverage = calculateAverage(allAverages);
    let highestAverage = findHighestGrade(allAverages);
    let lowestAverage = findLowestGrade(allAverages);
    let passingStudents = classGrades.filter(student => student.status === "PASSING").length;
    let classPassingRate = (passingStudents / totalStudents) * 100;
    
    // Grade distribution
    let gradeDistribution = { A: 0, B: 0, C: 0, D: 0, F: 0 };
    classGrades.forEach(student => {
        gradeDistribution[student.letterGrade]++;
    });
    
    return {
        totalStudents,
        classAverage: Math.round(classAverage * 100) / 100,
        highestAverage,
        lowestAverage,
        passingStudents,
        failingStudents: totalStudents - passingStudents,
        classPassingRate: Math.round(classPassingRate * 100) / 100,
        gradeDistribution
    };
}

// Test multiple students management
addStudent("Alice Johnson", [95, 88, 92, 89, 94]);
addStudent("Bob Wilson", [78, 82, 75, 80, 77]);
addStudent("Carol Davis", [65, 70, 68, 72, 69]);
addStudent("David Brown", [45, 52, 48, 55, 50]);

console.log("Class Statistics:");
let classStats = getClassStatistics();
console.log(`Total Students: ${classStats.totalStudents}`);
console.log(`Class Average: ${classStats.classAverage}%`);
console.log(`Highest Student Average: ${classStats.highestAverage}%`);
console.log(`Lowest Student Average: ${classStats.lowestAverage}%`);
console.log(`Passing Students: ${classStats.passingStudents}/${classStats.totalStudents}`);
console.log(`Class Passing Rate: ${classStats.classPassingRate}%`);
console.log("Grade Distribution:", classStats.gradeDistribution);

// ===== SECTION 5: ADVANCED FEATURES =====
console.log("\n5. Advanced Features:");

// TODO: Create a function for weighted grades
function calculateWeightedAverage(grades, weights) {
    // Your code here
    // grades and weights should be arrays of same length
    // Example: grades=[90, 85, 88], weights=[0.3, 0.3, 0.4]
    
    if (!Array.isArray(grades) || !Array.isArray(weights)) {
        return null;
    }
    
    if (grades.length !== weights.length) {
        return null;
    }
    
    // Check if weights sum to 1
    let weightSum = weights.reduce((sum, weight) => sum + weight, 0);
    if (Math.abs(weightSum - 1) > 0.001) { // Allow small floating point errors
        return null;
    }
    
    let weightedSum = 0;
    for (let i = 0; i < grades.length; i++) {
        weightedSum += grades[i] * weights[i];
    }
    
    return weightedSum;
}

// TODO: Create a function to predict final grade
function predictFinalGrade(currentGrades, targetGrade, remainingWeight) {
    // Your code here
    // Calculate what score is needed on remaining assignments
    
    let currentWeight = 1 - remainingWeight;
    let currentWeightedAverage = calculateAverage(currentGrades) * currentWeight;
    let neededWeightedPoints = targetGrade - currentWeightedAverage;
    let neededScore = neededWeightedPoints / remainingWeight;
    
    return Math.round(neededScore * 100) / 100;
}

// Test advanced features
console.log("Testing weighted average:");
let examGrades = [85, 90, 88];
let examWeights = [0.3, 0.3, 0.4];
console.log(`Weighted average: ${calculateWeightedAverage(examGrades, examWeights).toFixed(2)}`);

console.log("Testing grade prediction:");
let currentScores = [85, 90, 78, 92];
let neededScore = predictFinalGrade(currentScores, 85, 0.2);
console.log(`To get 85% overall, you need ${neededScore}% on remaining 20% of assignments`);

// ===== BONUS CHALLENGES =====
console.log("\n=== Bonus Challenges ===");

// BONUS 1: Create a grade curve function
function applyCurve(grades, curvePoints) {
    // Your code here
    // Add curve points to all grades, but cap at 100
    
    return grades.map(grade => Math.min(grade + curvePoints, 100));
}

// BONUS 2: Create a function to drop lowest grades
function dropLowestGrades(grades, dropCount) {
    // Your code here
    // Remove the lowest 'dropCount' grades from the array
    
    if (dropCount >= grades.length) {
        return [];
    }
    
    let sortedGrades = [...grades].sort((a, b) => b - a); // Sort descending
    return sortedGrades.slice(0, grades.length - dropCount);
}

// BONUS 3: Create a grade trend analysis
function analyzeGradeTrend(grades) {
    // Your code here
    // Determine if grades are improving, declining, or stable
    
    if (grades.length < 2) {
        return "Insufficient data";
    }
    
    let improvements = 0;
    let declines = 0;
    
    for (let i = 1; i < grades.length; i++) {
        if (grades[i] > grades[i-1]) {
            improvements++;
        } else if (grades[i] < grades[i-1]) {
            declines++;
        }
    }
    
    if (improvements > declines) {
        return "Improving";
    } else if (declines > improvements) {
        return "Declining";
    } else {
        return "Stable";
    }
}

console.log("Testing bonus features:");
let originalGrades = [75, 80, 70, 85, 78];
console.log(`Original grades: ${originalGrades}`);
console.log(`After 5-point curve: ${applyCurve(originalGrades, 5)}`);
console.log(`Drop 1 lowest: ${dropLowestGrades(originalGrades, 1)}`);
console.log(`Grade trend: ${analyzeGradeTrend(originalGrades)}`);

console.log("\n=== Grade Calculator Exercise Complete ===");

/*
SELF-CHECK QUESTIONS:
1. Do your grade calculation functions work with different inputs?
2. Does your code handle edge cases (empty arrays, invalid grades)?
3. Can you generate comprehensive reports for students?
4. Does your class management system work correctly?
5. Do your advanced features (weighted grades, predictions) work?
6. Can you explain the logic behind each function?

NEXT STEPS:
1. Test with more diverse data sets
2. Add input validation for all functions
3. Create a menu-driven interface
4. Add more statistical analysis features
5. Consider adding grade history tracking
*/
