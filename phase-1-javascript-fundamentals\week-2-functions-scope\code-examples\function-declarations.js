// Week 2 Day 5: Function Declarations and Expressions
// Date: [Enter today's date]

console.log("=== JavaScript Function Declarations and Expressions ===");

// ===== FUNCTION DECLARATIONS =====
console.log("\n1. Function Declarations:");

// Basic function declaration
function greet(name) {
    return `Hello, ${name}!`;
}

// Function declarations are hoisted
console.log("Calling hoisted function:", sayHello("World")); // This works!

function sayHello(name) {
    return `Hi there, ${name}!`;
}

// Function with multiple parameters
function calculateArea(length, width) {
    return length * width;
}

// Function with default parameters
function createUser(name, age = 18, role = "user") {
    return {
        name: name,
        age: age,
        role: role,
        id: Math.random().toString(36).substr(2, 9)
    };
}

// Function with rest parameters
function sum(...numbers) {
    let total = 0;
    for (let num of numbers) {
        total += num;
    }
    return total;
}

console.log("Basic greeting:", greet("Alice"));
console.log("Area calculation:", calculateArea(5, 3));
console.log("User with defaults:", createUser("Bob"));
console.log("User with all params:", createUser("<PERSON>", 25, "admin"));
console.log("Sum of numbers:", sum(1, 2, 3, 4, 5));

// ===== FUNCTION EXPRESSIONS =====
console.log("\n2. Function Expressions:");

// Basic function expression
const multiply = function(a, b) {
    return a * b;
};

// Named function expression (useful for debugging)
const divide = function divideNumbers(a, b) {
    if (b === 0) {
        throw new Error("Cannot divide by zero");
    }
    return a / b;
};

// Function expressions are NOT hoisted
// console.log(subtract(10, 5)); // This would cause an error

const subtract = function(a, b) {
    return a - b;
};

console.log("Multiplication:", multiply(6, 7));
console.log("Division:", divide(15, 3));
console.log("Subtraction:", subtract(10, 4));

// ===== ARROW FUNCTIONS =====
console.log("\n3. Arrow Functions:");

// Basic arrow function
const square = (x) => x * x;

// Arrow function with single parameter (parentheses optional)
const cube = x => x * x * x;

// Arrow function with no parameters
const getRandomNumber = () => Math.random();

// Arrow function with multiple parameters
const getFullName = (first, last) => `${first} ${last}`;

// Arrow function with block body
const processOrder = (items, discount) => {
    let total = items.reduce((sum, item) => sum + item.price, 0);
    let discountAmount = total * (discount / 100);
    let finalTotal = total - discountAmount;
    
    return {
        subtotal: total,
        discount: discountAmount,
        total: finalTotal
    };
};

console.log("Square of 5:", square(5));
console.log("Cube of 3:", cube(3));
console.log("Random number:", getRandomNumber());
console.log("Full name:", getFullName("John", "Doe"));

let orderItems = [
    { name: "Laptop", price: 999 },
    { name: "Mouse", price: 25 },
    { name: "Keyboard", price: 75 }
];
console.log("Order processing:", processOrder(orderItems, 10));

// ===== FUNCTION PARAMETERS AND ARGUMENTS =====
console.log("\n4. Advanced Parameter Patterns:");

// Destructuring parameters
function displayUser({name, age, email, city = "Unknown"}) {
    console.log(`Name: ${name}`);
    console.log(`Age: ${age}`);
    console.log(`Email: ${email}`);
    console.log(`City: ${city}`);
}

// Array destructuring in parameters
function getCoordinates([x, y, z = 0]) {
    return { x, y, z };
}

// Mixed parameters with rest
function logMessage(level, message, ...details) {
    console.log(`[${level.toUpperCase()}] ${message}`);
    if (details.length > 0) {
        console.log("Details:", details.join(", "));
    }
}

console.log("User display:");
displayUser({
    name: "Jane Smith",
    age: 28,
    email: "<EMAIL>"
});

console.log("Coordinates:", getCoordinates([10, 20]));
console.log("3D Coordinates:", getCoordinates([10, 20, 30]));

logMessage("info", "User logged in", "IP: ***********", "Browser: Chrome");

// ===== RETURN VALUES =====
console.log("\n5. Return Values and Early Returns:");

// Function with multiple return paths
function categorizeAge(age) {
    if (age < 0) {
        return "Invalid age";
    }
    
    if (age < 13) {
        return "Child";
    }
    
    if (age < 20) {
        return "Teenager";
    }
    
    if (age < 60) {
        return "Adult";
    }
    
    return "Senior";
}

// Function returning different data types
function processInput(input) {
    if (typeof input === "number") {
        return input * 2;
    }
    
    if (typeof input === "string") {
        return input.toUpperCase();
    }
    
    if (Array.isArray(input)) {
        return input.length;
    }
    
    return null;
}

// Function returning an object
function createCalculator(initialValue = 0) {
    return {
        value: initialValue,
        add: function(n) { this.value += n; return this; },
        subtract: function(n) { this.value -= n; return this; },
        multiply: function(n) { this.value *= n; return this; },
        divide: function(n) { 
            if (n !== 0) this.value /= n; 
            return this; 
        },
        getValue: function() { return this.value; }
    };
}

console.log("Age categories:");
console.log("Age 5:", categorizeAge(5));
console.log("Age 16:", categorizeAge(16));
console.log("Age 30:", categorizeAge(30));
console.log("Age 70:", categorizeAge(70));

console.log("Input processing:");
console.log("Number 5:", processInput(5));
console.log("String 'hello':", processInput("hello"));
console.log("Array [1,2,3]:", processInput([1, 2, 3]));

console.log("Calculator chaining:");
let calc = createCalculator(10);
let result = calc.add(5).multiply(2).subtract(3).getValue();
console.log("Result:", result);

// ===== FUNCTION SCOPE AND VARIABLES =====
console.log("\n6. Function Scope Examples:");

// Global variable
let globalCounter = 0;

function incrementGlobal() {
    globalCounter++;
    return globalCounter;
}

// Function with local variables
function createCounter() {
    let count = 0; // Local variable
    
    function increment() {
        count++;
        return count;
    }
    
    return increment;
}

// Function parameters shadow global variables
let message = "Global message";

function showMessage(message) {
    console.log("Function message:", message); // Uses parameter, not global
}

console.log("Global counter increments:");
console.log(incrementGlobal()); // 1
console.log(incrementGlobal()); // 2

console.log("Local counter:");
let counter1 = createCounter();
let counter2 = createCounter();
console.log("Counter 1:", counter1()); // 1
console.log("Counter 1:", counter1()); // 2
console.log("Counter 2:", counter2()); // 1 (independent)

console.log("Global message:", message);
showMessage("Function parameter message");

// ===== PRACTICAL EXAMPLES =====
console.log("\n7. Practical Function Examples:");

// Utility functions
const utils = {
    // Validation functions
    isEmail: email => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email),
    isPhone: phone => /^\d{10}$/.test(phone.replace(/\D/g, '')),
    
    // String utilities
    capitalize: str => str.charAt(0).toUpperCase() + str.slice(1).toLowerCase(),
    truncate: (str, length) => str.length > length ? str.slice(0, length) + '...' : str,
    
    // Number utilities
    formatCurrency: amount => `$${amount.toFixed(2)}`,
    randomBetween: (min, max) => Math.floor(Math.random() * (max - min + 1)) + min
};

// Form validation function
function validateForm(formData) {
    const errors = [];
    
    if (!formData.name || formData.name.trim().length < 2) {
        errors.push("Name must be at least 2 characters");
    }
    
    if (!utils.isEmail(formData.email)) {
        errors.push("Invalid email format");
    }
    
    if (!utils.isPhone(formData.phone)) {
        errors.push("Invalid phone number");
    }
    
    return {
        isValid: errors.length === 0,
        errors: errors
    };
}

// Data processing function
function processStudentGrades(students) {
    return students.map(student => {
        const average = student.grades.reduce((sum, grade) => sum + grade, 0) / student.grades.length;
        const letterGrade = average >= 90 ? 'A' : average >= 80 ? 'B' : average >= 70 ? 'C' : average >= 60 ? 'D' : 'F';
        
        return {
            ...student,
            average: Math.round(average * 100) / 100,
            letterGrade: letterGrade,
            status: average >= 60 ? 'Passing' : 'Failing'
        };
    });
}

console.log("Utility functions:");
console.log("Email validation:", utils.isEmail("<EMAIL>"));
console.log("Capitalize:", utils.capitalize("hello WORLD"));
console.log("Format currency:", utils.formatCurrency(123.456));
console.log("Random number:", utils.randomBetween(1, 10));

console.log("Form validation:");
let formData = {
    name: "John",
    email: "<EMAIL>",
    phone: "1234567890"
};
console.log(validateForm(formData));

console.log("Student grade processing:");
let students = [
    { name: "Alice", grades: [85, 90, 88] },
    { name: "Bob", grades: [75, 80, 70] }
];
console.log(processStudentGrades(students));

console.log("\n=== Function Declarations and Expressions Complete ===");

// Practice exercises
console.log("\n=== Practice Exercises ===");
console.log("Try these exercises:");
console.log("1. Create a function that uses all parameter types (default, rest, destructuring)");
console.log("2. Write both function declaration and arrow function versions of the same logic");
console.log("3. Create a utility library with various helper functions");
console.log("4. Practice function hoisting by calling functions before declaring them");
