# Week 3: Asynchronous JavaScript - Learning Activities

## Learning Objectives
By the end of this week, you will:
- [ ] Understand the JavaScript event loop and non-blocking execution
- [ ] Master callback functions and avoid callback hell
- [ ] Create and use Promises for asynchronous operations
- [ ] Use async/await for clean asynchronous code
- [ ] Handle errors in asynchronous operations effectively
- [ ] Understand Promise methods (all, race, allSettled)
- [ ] Build real-world asynchronous applications

## Daily Breakdown

### Day 11: Event Loop and Callbacks
**Duration:** 2-3 hours
**Focus:** Understanding asynchronous JavaScript fundamentals

#### Morning Session (1 hour)
- [ ] Read: [notes/day-11-event-loop-callbacks.md](notes/day-11-event-loop-callbacks.md)
- [ ] Watch: [JavaScript Event Loop Explained](https://www.youtube.com/watch?v=8aGhZQkoFbQ)
- [ ] Run: [code-examples/event-loop-demo.js](code-examples/event-loop-demo.js)
- [ ] Run: [code-examples/callback-examples.js](code-examples/callback-examples.js)

#### Evening Session (1.5 hours)
- [ ] Complete: [exercises/callback-practice.js](exercises/callback-practice.js)
- [ ] Practice: setTimeout and setInterval with callbacks
- [ ] Update: [WEEK-3-PROGRESS.md](WEEK-3-PROGRESS.md)

#### Success Criteria
- [ ] Can explain how the event loop works
- [ ] Understands the difference between synchronous and asynchronous code
- [ ] Can write and use callback functions
- [ ] Recognizes callback hell and its problems

### Day 12: Promises Fundamentals
**Duration:** 2-3 hours
**Focus:** Creating and using Promises

#### Morning Session (1 hour)
- [ ] Read: [notes/day-12-promises-basics.md](notes/day-12-promises-basics.md)
- [ ] Run: [code-examples/promise-basics.js](code-examples/promise-basics.js)
- [ ] Practice: Creating Promises with resolve and reject

#### Evening Session (1.5 hours)
- [ ] Complete: [exercises/promise-creation.js](exercises/promise-creation.js)
- [ ] Practice: Promise chaining with .then() and .catch()
- [ ] Update: Progress tracking

#### Success Criteria
- [ ] Can create Promises using the Promise constructor
- [ ] Understands Promise states (pending, fulfilled, rejected)
- [ ] Can chain Promises with .then() and .catch()
- [ ] Can handle Promise errors properly

### Day 13: Advanced Promise Patterns
**Duration:** 2-3 hours
**Focus:** Promise methods and advanced patterns

#### Morning Session (1 hour)
- [ ] Read: [notes/day-13-promise-methods.md](notes/day-13-promise-methods.md)
- [ ] Run: [code-examples/promise-methods.js](code-examples/promise-methods.js)
- [ ] Practice: Promise.all(), Promise.race(), Promise.allSettled()

#### Evening Session (1.5 hours)
- [ ] Complete: [exercises/promise-patterns.js](exercises/promise-patterns.js)
- [ ] Practice: Converting callbacks to Promises
- [ ] Update: Progress tracking

#### Success Criteria
- [ ] Can use Promise.all() for parallel operations
- [ ] Can use Promise.race() for timeout scenarios
- [ ] Can convert callback-based functions to Promises
- [ ] Understands when to use different Promise methods

### Day 14: Async/Await
**Duration:** 2-3 hours
**Focus:** Modern asynchronous JavaScript with async/await

#### Morning Session (1 hour)
- [ ] Read: [notes/day-14-async-await.md](notes/day-14-async-await.md)
- [ ] Run: [code-examples/async-await-examples.js](code-examples/async-await-examples.js)
- [ ] Practice: Converting Promises to async/await

#### Evening Session (1.5 hours)
- [ ] Complete: [exercises/async-await-practice.js](exercises/async-await-practice.js)
- [ ] Practice: Error handling with try/catch in async functions
- [ ] Update: Progress tracking

#### Success Criteria
- [ ] Can write async functions and use await
- [ ] Can handle errors in async/await with try/catch
- [ ] Can convert Promise chains to async/await
- [ ] Understands when async/await is preferable to Promises

### Day 15: Error Handling in Async Code
**Duration:** 2-3 hours
**Focus:** Comprehensive error handling strategies

#### Morning Session (1 hour)
- [ ] Read: [notes/day-15-async-error-handling.md](notes/day-15-async-error-handling.md)
- [ ] Run: [code-examples/async-error-handling.js](code-examples/async-error-handling.js)
- [ ] Practice: Different error handling patterns

#### Evening Session (1.5 hours)
- [ ] Complete: [exercises/error-handling-practice.js](exercises/error-handling-practice.js)
- [ ] Practice: Building robust async functions
- [ ] Update: Progress tracking

#### Success Criteria
- [ ] Can handle errors in callbacks, Promises, and async/await
- [ ] Can implement retry logic for failed operations
- [ ] Can create custom error types for async operations
- [ ] Can build fault-tolerant asynchronous systems

### Day 16: Week 3 Project - Async Data Processor
**Duration:** 3-4 hours
**Focus:** Building a comprehensive asynchronous application

#### Morning Session (2 hours)
- [ ] Plan: [projects/async-data-processor/README.md](projects/async-data-processor/README.md)
- [ ] Start: Building the async data processing pipeline
- [ ] Implement: File operations with Promises and async/await

#### Evening Session (2 hours)
- [ ] Complete: All project features
- [ ] Test: [projects/async-data-processor/tests.js](projects/async-data-processor/tests.js)
- [ ] Document: Add comprehensive error handling
- [ ] Update: Final progress tracking

#### Success Criteria
- [ ] Project uses Promises and async/await effectively
- [ ] Implements comprehensive error handling
- [ ] Demonstrates parallel and sequential async operations
- [ ] Code is clean and handles edge cases

## Week Summary Checklist
- [ ] All daily activities completed
- [ ] Weekly project finished and tested
- [ ] Self-assessment completed
- [ ] Notes organized and reviewed
- [ ] Ready for Week 4 concepts

## Resources Used This Week
- [ ] MDN Web Docs - Asynchronous JavaScript
- [ ] JavaScript.info - Promises and Async/Await
- [ ] Node.js Documentation - File System API
- [ ] YouTube tutorials (list specific ones you found helpful)

## Challenges & Solutions
Document any challenges you faced and how you overcame them:

### Challenge 1: Understanding the Event Loop
- **Problem**: [Describe the issue]
- **Solution**: [How you resolved it]
- **Key Learning**: [What you learned]

### Challenge 2: Promise Chaining Confusion
- **Problem**: [Describe the issue]
- **Solution**: [How you resolved it]
- **Key Learning**: [What you learned]

### Challenge 3: Async/Await Error Handling
- **Problem**: [Describe the issue]
- **Solution**: [How you resolved it]
- **Key Learning**: [What you learned]

## Code Quality Checklist
Before moving to Week 4, ensure your code:
- [ ] Uses appropriate async patterns for different scenarios
- [ ] Has comprehensive error handling for all async operations
- [ ] Avoids callback hell and Promise anti-patterns
- [ ] Uses async/await for cleaner, more readable code
- [ ] Handles both success and failure cases properly

## Preparation for Week 4
- [ ] Review any weak areas from Week 3
- [ ] Read introduction to modern JavaScript features
- [ ] Understand ES6+ syntax and features
- [ ] Set up Week 4 folder structure

## Self-Reflection Questions
1. Which asynchronous concept did you find most challenging this week?
2. How has async/await changed your approach to asynchronous code?
3. What strategies do you use to debug asynchronous code?
4. How do you decide between callbacks, Promises, and async/await?
5. What are you most excited to learn about modern JavaScript features?

## Week 3 Portfolio Items
Add these completed items to your portfolio:
- [ ] Async Data Processor project
- [ ] Best examples of Promise usage and async/await
- [ ] Screenshots of working async applications
- [ ] Reflection notes on asynchronous programming patterns

## Advanced Concepts Covered
- **Event Loop**: Understanding JavaScript's concurrency model
- **Promise States**: Pending, fulfilled, and rejected states
- **Promise Chaining**: Building complex async workflows
- **Promise Methods**: all, race, allSettled for different scenarios
- **Async/Await**: Modern syntax for cleaner async code
- **Error Propagation**: How errors flow through async operations

## Real-World Applications
- **API Calls**: Making HTTP requests to external services
- **File Operations**: Reading and writing files asynchronously
- **Database Operations**: Querying databases without blocking
- **User Interfaces**: Handling user interactions asynchronously
- **Data Processing**: Processing large datasets without freezing the UI

## Common Async Patterns Learned
- **Sequential Processing**: One operation after another
- **Parallel Processing**: Multiple operations simultaneously
- **Race Conditions**: First operation to complete wins
- **Timeout Handling**: Setting time limits on operations
- **Retry Logic**: Automatically retrying failed operations
- **Circuit Breaker**: Preventing cascading failures

## Performance Considerations
- **Non-blocking Operations**: Keeping the main thread responsive
- **Memory Management**: Avoiding memory leaks in long-running async operations
- **Error Recovery**: Graceful degradation when operations fail
- **Resource Cleanup**: Properly cleaning up async resources

---

**Congratulations on completing Week 3!** 🎉

You've mastered asynchronous JavaScript programming! Week 4 will introduce you to modern JavaScript features and comprehensive error handling - get ready to write professional-grade JavaScript code!
