# Day 3: File System Operations

## 🎯 Learning Objectives
- Master synchronous and asynchronous file operations
- Understand Promise-based file handling
- Implement proper error handling for file operations
- Work with directories and file metadata
- Build efficient file processing utilities

## 📁 File System Module Overview

### Importing the File System Module
```javascript
// CommonJS
const fs = require('fs');
const fsPromises = require('fs').promises;

// ES Modules
import fs from 'fs';
import { promises as fsPromises } from 'fs';
```

## 📖 Reading Files

### Synchronous File Reading
```javascript
const fs = require('fs');

try {
    // Read text file
    const data = fs.readFileSync('data.txt', 'utf8');
    console.log('File content:', data);
    
    // Read binary file
    const buffer = fs.readFileSync('image.jpg');
    console.log('File size:', buffer.length, 'bytes');
    
} catch (error) {
    console.error('Error reading file:', error.message);
}
```

### Asynchronous File Reading (Callback)
```javascript
const fs = require('fs');

fs.readFile('data.txt', 'utf8', (err, data) => {
    if (err) {
        console.error('Error reading file:', err.message);
        return;
    }
    console.log('File content:', data);
});

console.log('This executes immediately (non-blocking)');
```

### Promise-based File Reading
```javascript
const fs = require('fs').promises;

// Using .then()
fs.readFile('data.txt', 'utf8')
    .then(data => {
        console.log('File content:', data);
    })
    .catch(error => {
        console.error('Error reading file:', error.message);
    });

// Using async/await
async function readFileAsync() {
    try {
        const data = await fs.readFile('data.txt', 'utf8');
        console.log('File content:', data);
    } catch (error) {
        console.error('Error reading file:', error.message);
    }
}
```

## ✍️ Writing Files

### Synchronous File Writing
```javascript
const fs = require('fs');

try {
    // Write text file
    fs.writeFileSync('output.txt', 'Hello, Node.js!', 'utf8');
    console.log('File written successfully');
    
    // Write JSON data
    const data = { name: 'John', age: 30 };
    fs.writeFileSync('data.json', JSON.stringify(data, null, 2));
    
} catch (error) {
    console.error('Error writing file:', error.message);
}
```

### Asynchronous File Writing
```javascript
const fs = require('fs');

const content = 'Hello, Node.js!';

fs.writeFile('output.txt', content, 'utf8', (err) => {
    if (err) {
        console.error('Error writing file:', err.message);
        return;
    }
    console.log('File written successfully');
});
```

### Promise-based File Writing
```javascript
const fs = require('fs').promises;

async function writeFileAsync() {
    try {
        await fs.writeFile('output.txt', 'Hello, Node.js!', 'utf8');
        console.log('File written successfully');
    } catch (error) {
        console.error('Error writing file:', error.message);
    }
}
```

### Appending to Files
```javascript
const fs = require('fs').promises;

async function appendToFile() {
    try {
        await fs.appendFile('log.txt', 'New log entry\n');
        console.log('Content appended successfully');
    } catch (error) {
        console.error('Error appending to file:', error.message);
    }
}
```

## 📂 Directory Operations

### Creating Directories
```javascript
const fs = require('fs').promises;

async function createDirectories() {
    try {
        // Create single directory
        await fs.mkdir('new-folder');
        
        // Create nested directories
        await fs.mkdir('path/to/nested/folder', { recursive: true });
        
        console.log('Directories created successfully');
    } catch (error) {
        if (error.code === 'EEXIST') {
            console.log('Directory already exists');
        } else {
            console.error('Error creating directory:', error.message);
        }
    }
}
```

### Reading Directories
```javascript
const fs = require('fs').promises;

async function listDirectory() {
    try {
        const files = await fs.readdir('.');
        console.log('Files in current directory:', files);
        
        // Get detailed file information
        const filesWithStats = await Promise.all(
            files.map(async (file) => {
                const stats = await fs.stat(file);
                return {
                    name: file,
                    isDirectory: stats.isDirectory(),
                    size: stats.size,
                    modified: stats.mtime
                };
            })
        );
        
        console.log('Detailed file information:', filesWithStats);
    } catch (error) {
        console.error('Error reading directory:', error.message);
    }
}
```

### Removing Directories
```javascript
const fs = require('fs').promises;

async function removeDirectory() {
    try {
        // Remove empty directory
        await fs.rmdir('empty-folder');
        
        // Remove directory and all contents (Node.js 14+)
        await fs.rm('folder-with-contents', { recursive: true, force: true });
        
        console.log('Directory removed successfully');
    } catch (error) {
        console.error('Error removing directory:', error.message);
    }
}
```

## 📊 File Information and Metadata

### Getting File Statistics
```javascript
const fs = require('fs').promises;

async function getFileInfo() {
    try {
        const stats = await fs.stat('data.txt');
        
        console.log('File information:');
        console.log('Size:', stats.size, 'bytes');
        console.log('Is file:', stats.isFile());
        console.log('Is directory:', stats.isDirectory());
        console.log('Created:', stats.birthtime);
        console.log('Modified:', stats.mtime);
        console.log('Accessed:', stats.atime);
        
    } catch (error) {
        console.error('Error getting file info:', error.message);
    }
}
```

### Checking File Existence
```javascript
const fs = require('fs').promises;

async function checkFileExists(filePath) {
    try {
        await fs.access(filePath, fs.constants.F_OK);
        return true;
    } catch (error) {
        return false;
    }
}

// Usage
async function example() {
    const exists = await checkFileExists('data.txt');
    console.log('File exists:', exists);
}
```

## 🔄 File Operations Utilities

### JSON File Handler
```javascript
const fs = require('fs').promises;

class JSONFileHandler {
    static async read(filePath) {
        try {
            const data = await fs.readFile(filePath, 'utf8');
            return JSON.parse(data);
        } catch (error) {
            if (error.code === 'ENOENT') {
                return null; // File doesn't exist
            }
            throw new Error(`Failed to read JSON file: ${error.message}`);
        }
    }
    
    static async write(filePath, data) {
        try {
            const jsonString = JSON.stringify(data, null, 2);
            await fs.writeFile(filePath, jsonString, 'utf8');
        } catch (error) {
            throw new Error(`Failed to write JSON file: ${error.message}`);
        }
    }
    
    static async update(filePath, updateFunction) {
        try {
            const currentData = await this.read(filePath) || {};
            const updatedData = updateFunction(currentData);
            await this.write(filePath, updatedData);
            return updatedData;
        } catch (error) {
            throw new Error(`Failed to update JSON file: ${error.message}`);
        }
    }
}

// Usage example
async function example() {
    // Write data
    await JSONFileHandler.write('users.json', [
        { id: 1, name: 'John', email: '<EMAIL>' },
        { id: 2, name: 'Jane', email: '<EMAIL>' }
    ]);
    
    // Read data
    const users = await JSONFileHandler.read('users.json');
    console.log('Users:', users);
    
    // Update data
    await JSONFileHandler.update('users.json', (data) => {
        data.push({ id: 3, name: 'Bob', email: '<EMAIL>' });
        return data;
    });
}
```

### File Backup Utility
```javascript
const fs = require('fs').promises;
const path = require('path');

class FileBackup {
    static async createBackup(filePath) {
        try {
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const ext = path.extname(filePath);
            const name = path.basename(filePath, ext);
            const dir = path.dirname(filePath);
            
            const backupPath = path.join(dir, `${name}.backup.${timestamp}${ext}`);
            
            await fs.copyFile(filePath, backupPath);
            console.log(`Backup created: ${backupPath}`);
            return backupPath;
        } catch (error) {
            throw new Error(`Failed to create backup: ${error.message}`);
        }
    }
    
    static async restoreBackup(backupPath, originalPath) {
        try {
            await fs.copyFile(backupPath, originalPath);
            console.log(`Backup restored to: ${originalPath}`);
        } catch (error) {
            throw new Error(`Failed to restore backup: ${error.message}`);
        }
    }
    
    static async listBackups(filePath) {
        try {
            const dir = path.dirname(filePath);
            const name = path.basename(filePath, path.extname(filePath));
            
            const files = await fs.readdir(dir);
            const backups = files.filter(file => 
                file.startsWith(`${name}.backup.`)
            );
            
            return backups.map(backup => path.join(dir, backup));
        } catch (error) {
            throw new Error(`Failed to list backups: ${error.message}`);
        }
    }
}
```

### Directory Walker
```javascript
const fs = require('fs').promises;
const path = require('path');

class DirectoryWalker {
    static async walkDirectory(dirPath, callback) {
        try {
            const items = await fs.readdir(dirPath);
            
            for (const item of items) {
                const fullPath = path.join(dirPath, item);
                const stats = await fs.stat(fullPath);
                
                await callback(fullPath, stats);
                
                if (stats.isDirectory()) {
                    await this.walkDirectory(fullPath, callback);
                }
            }
        } catch (error) {
            console.error(`Error walking directory ${dirPath}:`, error.message);
        }
    }
    
    static async findFiles(dirPath, pattern) {
        const foundFiles = [];
        
        await this.walkDirectory(dirPath, async (filePath, stats) => {
            if (stats.isFile() && pattern.test(path.basename(filePath))) {
                foundFiles.push(filePath);
            }
        });
        
        return foundFiles;
    }
    
    static async getDirectorySize(dirPath) {
        let totalSize = 0;
        
        await this.walkDirectory(dirPath, async (filePath, stats) => {
            if (stats.isFile()) {
                totalSize += stats.size;
            }
        });
        
        return totalSize;
    }
}

// Usage example
async function example() {
    // Find all JavaScript files
    const jsFiles = await DirectoryWalker.findFiles('.', /\.js$/);
    console.log('JavaScript files:', jsFiles);
    
    // Get directory size
    const size = await DirectoryWalker.getDirectorySize('.');
    console.log('Directory size:', size, 'bytes');
}
```

## ⚠️ Error Handling Best Practices

### Common File System Errors
```javascript
const fs = require('fs').promises;

async function handleFileErrors() {
    try {
        const data = await fs.readFile('nonexistent.txt', 'utf8');
    } catch (error) {
        switch (error.code) {
            case 'ENOENT':
                console.error('File not found');
                break;
            case 'EACCES':
                console.error('Permission denied');
                break;
            case 'EISDIR':
                console.error('Expected file, got directory');
                break;
            case 'EMFILE':
                console.error('Too many open files');
                break;
            default:
                console.error('Unknown error:', error.message);
        }
    }
}
```

### Robust File Operations
```javascript
const fs = require('fs').promises;

class RobustFileOperations {
    static async safeReadFile(filePath, defaultValue = null) {
        try {
            return await fs.readFile(filePath, 'utf8');
        } catch (error) {
            if (error.code === 'ENOENT') {
                return defaultValue;
            }
            throw error;
        }
    }
    
    static async safeWriteFile(filePath, data) {
        const tempPath = `${filePath}.tmp`;
        
        try {
            // Write to temporary file first
            await fs.writeFile(tempPath, data, 'utf8');
            
            // Atomic rename
            await fs.rename(tempPath, filePath);
        } catch (error) {
            // Clean up temporary file if it exists
            try {
                await fs.unlink(tempPath);
            } catch (cleanupError) {
                // Ignore cleanup errors
            }
            throw error;
        }
    }
    
    static async ensureDirectory(dirPath) {
        try {
            await fs.mkdir(dirPath, { recursive: true });
        } catch (error) {
            if (error.code !== 'EEXIST') {
                throw error;
            }
        }
    }
}
```

## 🎯 Performance Considerations

### Efficient File Processing
```javascript
// Good: Process files asynchronously
async function processFilesEfficiently(filePaths) {
    const results = await Promise.all(
        filePaths.map(async (filePath) => {
            try {
                const data = await fs.readFile(filePath, 'utf8');
                return { filePath, data, success: true };
            } catch (error) {
                return { filePath, error: error.message, success: false };
            }
        })
    );
    
    return results;
}

// Better: Control concurrency
async function processFilesWithConcurrency(filePaths, concurrency = 5) {
    const results = [];
    
    for (let i = 0; i < filePaths.length; i += concurrency) {
        const batch = filePaths.slice(i, i + concurrency);
        const batchResults = await Promise.all(
            batch.map(async (filePath) => {
                try {
                    const data = await fs.readFile(filePath, 'utf8');
                    return { filePath, data, success: true };
                } catch (error) {
                    return { filePath, error: error.message, success: false };
                }
            })
        );
        results.push(...batchResults);
    }
    
    return results;
}
```

## 📝 Practice Exercises

### Exercise 1: Configuration Manager
Build a configuration manager that reads/writes JSON configuration files.

### Exercise 2: Log File Analyzer
Create a utility to analyze log files and extract statistics.

### Exercise 3: File Synchronizer
Build a tool to synchronize files between directories.

## 🔗 Additional Resources

- [Node.js File System Documentation](https://nodejs.org/api/fs.html)
- [File System Promises API](https://nodejs.org/api/fs.html#fs_promises_api)

## 🏁 Day 3 Summary

Today you learned:
- ✅ Synchronous vs asynchronous file operations
- ✅ Promise-based file handling
- ✅ Directory operations and file metadata
- ✅ Error handling for file operations
- ✅ Building file processing utilities

**Next:** Day 4 will cover Path, OS, and Crypto modules.
