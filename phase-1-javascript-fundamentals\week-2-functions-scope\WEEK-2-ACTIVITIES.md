# Week 2: Functions, Scope & Closures - Learning Activities

## Learning Objectives
By the end of this week, you will:
- [ ] Master different types of function declarations and expressions
- [ ] Understand function parameters, arguments, and return values
- [ ] Comprehend scope (global, function, block) and the scope chain
- [ ] Create and use closures for data privacy and specialized functions
- [ ] Understand 'this' keyword binding in different contexts
- [ ] Use higher-order functions and functional programming concepts

## Daily Breakdown

### Day 5: Function Basics and Declarations
**Duration:** 2-3 hours
**Focus:** Function fundamentals and different declaration types

#### Morning Session (1 hour)
- [ ] Read: [notes/day-5-function-basics.md](notes/day-5-function-basics.md)
- [ ] Watch: [JavaScript Functions Explained](https://www.youtube.com/watch?v=N8ap4k_1QEQ)
- [ ] Run: [code-examples/function-declarations.js](code-examples/function-declarations.js)
- [ ] Run: [code-examples/arrow-functions.js](code-examples/arrow-functions.js)

#### Evening Session (1.5 hours)
- [ ] Complete: [exercises/function-library.js](exercises/function-library.js)
- [ ] Practice: Create functions with different parameter patterns
- [ ] Update: [WEEK-2-PROGRESS.md](WEEK-2-PROGRESS.md)

#### Success Criteria
- [ ] Can write function declarations, expressions, and arrow functions
- [ ] Understands hoisting behavior of different function types
- [ ] Can use default parameters and rest parameters
- [ ] Comfortable with return statements and values

### Day 6: Higher-Order Functions and Callbacks
**Duration:** 2-3 hours
**Focus:** Functions as first-class citizens

#### Morning Session (1 hour)
- [ ] Read: [notes/day-6-higher-order-functions.md](notes/day-6-higher-order-functions.md)
- [ ] Run: [code-examples/higher-order-functions.js](code-examples/higher-order-functions.js)
- [ ] Practice: Array methods (map, filter, reduce, forEach)

#### Evening Session (1.5 hours)
- [ ] Complete: [exercises/callback-practice.js](exercises/callback-practice.js)
- [ ] Practice: Create your own higher-order functions
- [ ] Update: Progress tracking

#### Success Criteria
- [ ] Can pass functions as arguments
- [ ] Can return functions from other functions
- [ ] Understands callback patterns
- [ ] Can use array methods effectively

### Day 7: Scope and the Scope Chain
**Duration:** 2-3 hours
**Focus:** Understanding variable scope and accessibility

#### Morning Session (1 hour)
- [ ] Read: [notes/day-7-scope-closures.md](notes/day-7-scope-closures.md)
- [ ] Run: [code-examples/scope-examples.js](code-examples/scope-examples.js)
- [ ] Experiment: Variable shadowing and scope chain

#### Evening Session (1.5 hours)
- [ ] Complete: [exercises/scope-challenges.js](exercises/scope-challenges.js)
- [ ] Practice: Nested function scope examples
- [ ] Update: Progress tracking

#### Success Criteria
- [ ] Understands global, function, and block scope
- [ ] Can predict variable accessibility in nested functions
- [ ] Knows the difference between var, let, and const scoping
- [ ] Can debug scope-related issues

### Day 8: Closures and Data Privacy
**Duration:** 2-3 hours
**Focus:** Mastering closures and their practical applications

#### Morning Session (1 hour)
- [ ] Read: [notes/day-8-closures-advanced.md](notes/day-8-closures-advanced.md)
- [ ] Run: [code-examples/closures-examples.js](code-examples/closures-examples.js)
- [ ] Practice: Creating private variables with closures

#### Evening Session (1.5 hours)
- [ ] Complete: [exercises/closure-practice.js](exercises/closure-practice.js)
- [ ] Practice: Module pattern with closures
- [ ] Update: Progress tracking

#### Success Criteria
- [ ] Can create and use closures effectively
- [ ] Understands how closures provide data privacy
- [ ] Can implement the module pattern
- [ ] Can create specialized functions using closures

### Day 9: 'this' Keyword and Context
**Duration:** 2-3 hours
**Focus:** Understanding 'this' binding in different contexts

#### Morning Session (1 hour)
- [ ] Read: [notes/day-9-this-keyword.md](notes/day-9-this-keyword.md)
- [ ] Run: [code-examples/this-binding.js](code-examples/this-binding.js)
- [ ] Practice: call, apply, and bind methods

#### Evening Session (1.5 hours)
- [ ] Complete: [exercises/this-context-practice.js](exercises/this-context-practice.js)
- [ ] Practice: Arrow functions vs regular functions with 'this'
- [ ] Update: Progress tracking

#### Success Criteria
- [ ] Understands how 'this' is determined
- [ ] Can use call, apply, and bind methods
- [ ] Knows when arrow functions bind 'this' differently
- [ ] Can debug 'this' context issues

### Day 10: Week 2 Project - Advanced Task Manager
**Duration:** 3-4 hours
**Focus:** Integrating all Week 2 concepts

#### Morning Session (2 hours)
- [ ] Plan: [projects/task-manager-advanced/README.md](projects/task-manager-advanced/README.md)
- [ ] Start: Building the advanced task manager with closures
- [ ] Implement: Private data and methods using closures

#### Evening Session (2 hours)
- [ ] Complete: All project features
- [ ] Test: [projects/task-manager-advanced/tests.js](projects/task-manager-advanced/tests.js)
- [ ] Document: Add comprehensive comments
- [ ] Update: Final progress tracking

#### Success Criteria
- [ ] Project uses closures for data privacy
- [ ] Implements higher-order functions for data processing
- [ ] Demonstrates proper 'this' binding
- [ ] Code is modular and well-organized

## Week Summary Checklist
- [ ] All daily activities completed
- [ ] Weekly project finished and tested
- [ ] Self-assessment completed
- [ ] Notes organized and reviewed
- [ ] Ready for Week 3 concepts

## Resources Used This Week
- [ ] MDN Web Docs - JavaScript Functions
- [ ] JavaScript.info - Functions and Closures
- [ ] Eloquent JavaScript - Higher-Order Functions
- [ ] YouTube tutorials (list specific ones you found helpful)

## Challenges & Solutions
Document any challenges you faced and how you overcame them:

### Challenge 1: Understanding Closures
- **Problem**: [Describe the issue]
- **Solution**: [How you resolved it]
- **Key Learning**: [What you learned]

### Challenge 2: 'this' Keyword Confusion
- **Problem**: [Describe the issue]
- **Solution**: [How you resolved it]
- **Key Learning**: [What you learned]

### Challenge 3: Higher-Order Function Logic
- **Problem**: [Describe the issue]
- **Solution**: [How you resolved it]
- **Key Learning**: [What you learned]

## Code Quality Checklist
Before moving to Week 3, ensure your code:
- [ ] Uses appropriate function types for different scenarios
- [ ] Implements closures where data privacy is needed
- [ ] Uses higher-order functions for data processing
- [ ] Has proper 'this' binding in object methods
- [ ] Follows functional programming principles where appropriate

## Preparation for Week 3
- [ ] Review any weak areas from Week 2
- [ ] Read introduction to asynchronous JavaScript
- [ ] Understand the concept of non-blocking code
- [ ] Set up Week 3 folder structure

## Self-Reflection Questions
1. Which function concept did you find most challenging this week?
2. How do closures change the way you think about data privacy?
3. When would you choose arrow functions over regular functions?
4. How has your understanding of scope improved?
5. What are you most excited to learn about asynchronous JavaScript?

## Week 2 Portfolio Items
Add these completed items to your portfolio:
- [ ] Advanced Task Manager project
- [ ] Best examples of closures and higher-order functions
- [ ] Screenshots of working function demonstrations
- [ ] Reflection notes on functional programming concepts

## Advanced Concepts Covered
- **Function Hoisting**: How different function types behave during compilation
- **Lexical Scoping**: How JavaScript determines variable accessibility
- **Closure Applications**: Module pattern, factory functions, data privacy
- **Functional Programming**: Pure functions, immutability, function composition
- **Context Binding**: Understanding and controlling 'this' in different scenarios

## Real-World Applications
- **Module Systems**: Using closures to create private modules
- **Event Handlers**: Understanding 'this' in DOM event callbacks
- **Data Processing**: Using higher-order functions for array manipulation
- **API Design**: Creating flexible functions with callbacks
- **State Management**: Using closures for maintaining private state

---

**Congratulations on completing Week 2!** 🎉

You've mastered the power of functions and scope in JavaScript. Week 3 will introduce you to asynchronous programming - get ready to learn about callbacks, promises, and async/await!
