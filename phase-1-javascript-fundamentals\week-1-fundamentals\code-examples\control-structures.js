// Week 1 Day 4: Control Structures Examples
// Date: [Enter today's date]

console.log("=== JavaScript Control Structures ===");

// ===== IF-ELSE STATEMENTS =====
console.log("\n1. If-Else Statements:");

// Simple if statement
let temperature = 25;
if (temperature > 30) {
    console.log("It's hot outside!");
}

// If-else statement
let age = 17;
if (age >= 18) {
    console.log("You can vote!");
} else {
    console.log("You cannot vote yet.");
}

// If-else-if chain
let score = 85;
if (score >= 90) {
    console.log("Grade: A - Excellent!");
} else if (score >= 80) {
    console.log("Grade: B - Good job!");
} else if (score >= 70) {
    console.log("Grade: C - Satisfactory");
} else if (score >= 60) {
    console.log("Grade: D - Needs improvement");
} else {
    console.log("Grade: F - Please study more");
}

// Nested if statements
let weather = "sunny";
let hasUmbrella = false;

if (weather === "rainy") {
    if (hasUmbrella) {
        console.log("You can go out with your umbrella!");
    } else {
        console.log("Better stay inside or get an umbrella.");
    }
} else {
    console.log("Perfect weather to go out!");
}

// ===== SWITCH STATEMENTS =====
console.log("\n2. Switch Statements:");

// Basic switch statement
let day = "Monday";
switch (day) {
    case "Monday":
        console.log("Start of the work week!");
        break;
    case "Tuesday":
        console.log("Tuesday blues...");
        break;
    case "Wednesday":
        console.log("Hump day!");
        break;
    case "Thursday":
        console.log("Almost there!");
        break;
    case "Friday":
        console.log("TGIF!");
        break;
    case "Saturday":
    case "Sunday":
        console.log("Weekend time!");
        break;
    default:
        console.log("Invalid day");
}

// Switch with fall-through (no break)
let month = 3;
let season;
switch (month) {
    case 12:
    case 1:
    case 2:
        season = "Winter";
        break;
    case 3:
    case 4:
    case 5:
        season = "Spring";
        break;
    case 6:
    case 7:
    case 8:
        season = "Summer";
        break;
    case 9:
    case 10:
    case 11:
        season = "Fall";
        break;
    default:
        season = "Invalid month";
}
console.log(`Month ${month} is in ${season}`);

// ===== FOR LOOPS =====
console.log("\n3. For Loops:");

// Basic for loop
console.log("Counting from 1 to 5:");
for (let i = 1; i <= 5; i++) {
    console.log(`Count: ${i}`);
}

// For loop with different increments
console.log("Even numbers from 0 to 10:");
for (let i = 0; i <= 10; i += 2) {
    console.log(i);
}

// Reverse for loop
console.log("Countdown from 5 to 1:");
for (let i = 5; i >= 1; i--) {
    console.log(i);
}

// For loop with arrays
let fruits = ["apple", "banana", "orange", "grape"];
console.log("Fruits list:");
for (let i = 0; i < fruits.length; i++) {
    console.log(`${i + 1}. ${fruits[i]}`);
}

// Nested for loops
console.log("Multiplication table (3x3):");
for (let i = 1; i <= 3; i++) {
    let row = "";
    for (let j = 1; j <= 3; j++) {
        row += `${i * j}\t`;
    }
    console.log(row);
}

// ===== WHILE LOOPS =====
console.log("\n4. While Loops:");

// Basic while loop
let count = 1;
console.log("While loop counting to 5:");
while (count <= 5) {
    console.log(`Count: ${count}`);
    count++;
}

// While loop with condition
let number = 1;
console.log("Powers of 2 less than 100:");
while (number < 100) {
    console.log(number);
    number *= 2;
}

// While loop for user input simulation
let attempts = 0;
let maxAttempts = 3;
let correctPassword = "secret123";
let userPassword = "wrong"; // Simulating user input

console.log("Password verification simulation:");
while (attempts < maxAttempts && userPassword !== correctPassword) {
    attempts++;
    console.log(`Attempt ${attempts}: Password incorrect`);
    
    // Simulate different password attempts
    if (attempts === 1) userPassword = "password";
    else if (attempts === 2) userPassword = "123456";
    else if (attempts === 3) userPassword = "secret123";
}

if (userPassword === correctPassword) {
    console.log("Access granted!");
} else {
    console.log("Access denied. Too many failed attempts.");
}

// ===== DO-WHILE LOOPS =====
console.log("\n5. Do-While Loops:");

// Basic do-while loop
let num = 1;
console.log("Do-while loop (executes at least once):");
do {
    console.log(`Number: ${num}`);
    num++;
} while (num <= 3);

// Do-while vs while comparison
let x = 10;
console.log("Do-while with false condition:");
do {
    console.log("This will execute once even though condition is false");
} while (x < 5);

console.log("While with false condition:");
while (x < 5) {
    console.log("This will never execute");
}

// ===== FOR-OF LOOPS =====
console.log("\n6. For-Of Loops (ES6):");

// For-of with arrays
let colors = ["red", "green", "blue", "yellow"];
console.log("Colors using for-of:");
for (let color of colors) {
    console.log(color);
}

// For-of with strings
let word = "JavaScript";
console.log("Letters in 'JavaScript':");
for (let letter of word) {
    console.log(letter);
}

// ===== FOR-IN LOOPS =====
console.log("\n7. For-In Loops:");

// For-in with objects
let person = {
    name: "John",
    age: 30,
    city: "New York",
    occupation: "Developer"
};

console.log("Person properties:");
for (let key in person) {
    console.log(`${key}: ${person[key]}`);
}

// For-in with arrays (not recommended)
let numbers = [10, 20, 30];
console.log("Array indices using for-in:");
for (let index in numbers) {
    console.log(`Index ${index}: ${numbers[index]}`);
}

// ===== BREAK AND CONTINUE =====
console.log("\n8. Break and Continue:");

// Break statement
console.log("Using break to exit loop early:");
for (let i = 1; i <= 10; i++) {
    if (i === 6) {
        console.log("Breaking at 6");
        break;
    }
    console.log(i);
}

// Continue statement
console.log("Using continue to skip iterations:");
for (let i = 1; i <= 10; i++) {
    if (i % 2 === 0) {
        continue; // Skip even numbers
    }
    console.log(`Odd number: ${i}`);
}

// Break and continue with labels
console.log("Nested loops with labeled break:");
outer: for (let i = 1; i <= 3; i++) {
    for (let j = 1; j <= 3; j++) {
        if (i === 2 && j === 2) {
            console.log("Breaking out of both loops");
            break outer;
        }
        console.log(`i=${i}, j=${j}`);
    }
}

// ===== PRACTICAL EXAMPLES =====
console.log("\n9. Practical Examples:");

// Number guessing game simulation
function numberGuessingGame() {
    let targetNumber = 7;
    let guess = 1;
    let attempts = 0;
    let maxAttempts = 5;
    
    console.log("Number Guessing Game (1-10):");
    
    while (attempts < maxAttempts) {
        attempts++;
        console.log(`Attempt ${attempts}: Guessing ${guess}`);
        
        if (guess === targetNumber) {
            console.log(`Congratulations! You found the number in ${attempts} attempts!`);
            return;
        } else if (guess < targetNumber) {
            console.log("Too low!");
        } else {
            console.log("Too high!");
        }
        
        // Simulate next guess
        guess += Math.floor(Math.random() * 3) + 1;
    }
    
    console.log(`Game over! The number was ${targetNumber}`);
}

numberGuessingGame();

// Grade statistics calculator
function calculateGradeStats() {
    let grades = [85, 92, 78, 96, 88, 73, 91, 84];
    let total = 0;
    let highest = grades[0];
    let lowest = grades[0];
    let passCount = 0;
    let passGrade = 70;
    
    console.log("\nGrade Statistics:");
    console.log("Grades:", grades.join(", "));
    
    for (let grade of grades) {
        total += grade;
        
        if (grade > highest) {
            highest = grade;
        }
        
        if (grade < lowest) {
            lowest = grade;
        }
        
        if (grade >= passGrade) {
            passCount++;
        }
    }
    
    let average = total / grades.length;
    let passRate = (passCount / grades.length) * 100;
    
    console.log(`Average: ${average.toFixed(2)}`);
    console.log(`Highest: ${highest}`);
    console.log(`Lowest: ${lowest}`);
    console.log(`Pass rate: ${passRate.toFixed(1)}%`);
}

calculateGradeStats();

console.log("\n=== Control Structures Complete ===");

// Practice exercises
console.log("\n=== Practice Exercises ===");
console.log("Try these exercises:");
console.log("1. Create a menu system using switch statements");
console.log("2. Build a simple ATM simulator with loops and conditions");
console.log("3. Write a function to find prime numbers using nested loops");
console.log("4. Create a pattern printer using nested for loops");
