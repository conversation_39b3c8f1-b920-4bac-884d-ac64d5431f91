# Week 1: Node.js Architecture & Core Modules - Learning Activities

## 🎯 Learning Objectives
By the end of this week, you will:
- [ ] Understand Node.js architecture and the event loop
- [ ] Master CommonJS and ES module systems
- [ ] Work confidently with file system operations
- [ ] Use core Node.js modules (path, os, crypto, process)
- [ ] Handle environment variables and configuration
- [ ] Build a complete CLI application using Node.js

## 📅 Daily Breakdown

### Day 1: Node.js Architecture & Event Loop
**Duration:** 2-3 hours
**Focus:** Understanding Node.js runtime and architecture

#### Morning Session (1 hour)
- [ ] **Read:** notes/day-1-nodejs-architecture.md
- [ ] **Watch:** Node.js Event Loop explanation videos
- [ ] **Run:** code-examples/event-loop-demo.js
- [ ] **Experiment:** Modify event loop examples

#### Evening Session (1.5 hours)
- [ ] **Complete:** exercises/event-loop-understanding.js
- [ ] **Practice:** Create your own event loop examples
- [ ] **Update:** WEEK-1-PROGRESS.md with today's learnings

#### Success Criteria
- [ ] Can explain how Node.js event loop works
- [ ] Understand difference between blocking and non-blocking operations
- [ ] Can predict execution order of async operations

### Day 2: Module Systems & NPM
**Duration:** 2-3 hours
**Focus:** CommonJS, ES modules, and package management

#### Morning Session (1 hour)
- [ ] **Read:** notes/day-2-modules-npm.md
- [ ] **Study:** CommonJS vs ES modules differences
- [ ] **Run:** code-examples/module-systems.js
- [ ] **Practice:** Create modules using both systems

#### Evening Session (1.5 hours)
- [ ] **Complete:** exercises/module-creator.js
- [ ] **Initialize:** Your first npm project
- [ ] **Install:** Popular npm packages and explore
- [ ] **Create:** Package.json with custom scripts

#### Success Criteria
- [ ] Can create and export modules using CommonJS
- [ ] Can create and export modules using ES modules
- [ ] Understand npm package management
- [ ] Can write custom npm scripts

### Day 3: File System Operations
**Duration:** 2-3 hours
**Focus:** Reading, writing, and manipulating files

#### Morning Session (1 hour)
- [ ] **Read:** notes/day-3-file-system.md
- [ ] **Study:** Synchronous vs asynchronous file operations
- [ ] **Run:** code-examples/file-operations.js
- [ ] **Practice:** Different file reading/writing methods

#### Evening Session (1.5 hours)
- [ ] **Complete:** exercises/file-manager.js
- [ ] **Build:** JSON file reader/writer utility
- [ ] **Implement:** Error handling for file operations
- [ ] **Test:** Edge cases and error scenarios

#### Success Criteria
- [ ] Can read and write files asynchronously
- [ ] Understand when to use sync vs async operations
- [ ] Can handle file operation errors gracefully
- [ ] Can work with JSON files effectively

### Day 4: Path, OS, and Crypto Modules
**Duration:** 2-3 hours
**Focus:** Core utility modules

#### Morning Session (1 hour)
- [ ] **Read:** notes/day-4-path-os-crypto.md
- [ ] **Study:** Path manipulation and OS information
- [ ] **Run:** code-examples/path-utilities.js
- [ ] **Run:** code-examples/crypto-examples.js

#### Evening Session (1.5 hours)
- [ ] **Complete:** exercises/system-info-tool.js
- [ ] **Build:** Password hashing utility
- [ ] **Create:** Cross-platform file path handler
- [ ] **Practice:** Cryptographic operations

#### Success Criteria
- [ ] Can manipulate file paths correctly
- [ ] Can gather system information
- [ ] Can implement secure password hashing
- [ ] Understand basic cryptographic operations

### Day 5: Process & Environment Variables
**Duration:** 2-3 hours
**Focus:** Process management and configuration

#### Morning Session (1 hour)
- [ ] **Read:** notes/day-5-process-environment.md
- [ ] **Study:** Process object and environment variables
- [ ] **Run:** code-examples/process-handling.js
- [ ] **Practice:** Command line argument parsing

#### Evening Session (1.5 hours)
- [ ] **Complete:** exercises/config-loader.js
- [ ] **Build:** Environment-based configuration system
- [ ] **Implement:** Graceful shutdown handling
- [ ] **Create:** Command line interface basics

#### Success Criteria
- [ ] Can handle command line arguments
- [ ] Can manage environment variables
- [ ] Can implement graceful shutdown
- [ ] Understand process lifecycle

### Day 6: Core Modules Integration
**Duration:** 2-3 hours
**Focus:** Combining multiple core modules

#### Morning Session (1 hour)
- [ ] **Read:** notes/day-6-core-modules.md
- [ ] **Review:** All core modules learned this week
- [ ] **Study:** Integration patterns and best practices
- [ ] **Plan:** Week project architecture

#### Evening Session (1.5 hours)
- [ ] **Complete:** exercises/log-analyzer.js
- [ ] **Start:** Week project planning
- [ ] **Design:** Application architecture
- [ ] **Setup:** Project structure and dependencies

#### Success Criteria
- [ ] Can combine multiple core modules effectively
- [ ] Understand Node.js application architecture
- [ ] Can design modular applications
- [ ] Ready to build complete CLI application

### Day 7: Project Development & Review
**Duration:** 3-4 hours
**Focus:** Building complete CLI application

#### Morning Session (2 hours)
- [ ] **Read:** notes/day-7-review-project.md
- [ ] **Build:** projects/task-manager-cli/
- [ ] **Implement:** Core functionality
- [ ] **Add:** Error handling and validation

#### Evening Session (2 hours)
- [ ] **Complete:** CLI application features
- [ ] **Test:** All functionality thoroughly
- [ ] **Document:** Usage and setup instructions
- [ ] **Review:** Week's learning and progress

#### Success Criteria
- [ ] Built complete CLI application
- [ ] Demonstrated all week's concepts
- [ ] Application handles errors gracefully
- [ ] Code is well-organized and documented

## 📋 Week Summary Checklist

### Technical Skills Mastered
- [ ] Node.js architecture and event loop understanding
- [ ] Module systems (CommonJS and ES modules)
- [ ] File system operations (sync and async)
- [ ] Core modules: path, os, crypto, process
- [ ] Environment variables and configuration
- [ ] NPM package management
- [ ] CLI application development

### Projects Completed
- [ ] Event loop demonstration
- [ ] Module system examples
- [ ] File manager utility
- [ ] System information tool
- [ ] Configuration loader
- [ ] Log analyzer
- [ ] **Main Project:** Task Manager CLI

### Code Quality Practices
- [ ] Proper error handling throughout
- [ ] Modular code organization
- [ ] Environment-based configuration
- [ ] Comprehensive documentation
- [ ] Testing and validation

## 🎯 Week 1 Project: Task Manager CLI

### Project Requirements
Build a command-line task management application that demonstrates all Week 1 concepts:

#### Core Features
1. **Task Management**
   - Add, list, update, delete tasks
   - Mark tasks as complete/incomplete
   - Set task priorities and due dates

2. **Data Persistence**
   - Store tasks in JSON files
   - Backup and restore functionality
   - Data validation and error handling

3. **Configuration**
   - Environment-based settings
   - Configurable data directory
   - User preferences

4. **CLI Interface**
   - Command line argument parsing
   - Interactive prompts
   - Colored output and formatting

#### Technical Requirements
- Use CommonJS modules for organization
- Implement proper error handling
- Use fs module for data persistence
- Use path module for file operations
- Use crypto module for task IDs
- Handle process signals gracefully
- Support environment variables

#### Project Structure
```
task-manager-cli/
├── package.json
├── README.md
├── .env.example
├── src/
│   ├── models/
│   │   └── Task.js
│   ├── services/
│   │   ├── TaskService.js
│   │   └── FileService.js
│   ├── utils/
│   │   ├── config.js
│   │   ├── logger.js
│   │   └── validators.js
│   └── cli.js
├── data/
│   └── tasks.json
└── tests/
    └── basic-tests.js
```

## 📚 Resources Used This Week

### Essential Reading
- [ ] Node.js Official Documentation
- [ ] NPM Documentation
- [ ] File System API Reference
- [ ] Core Modules Documentation

### Helpful Tools
- [ ] Node.js REPL for experimentation
- [ ] VS Code with Node.js extensions
- [ ] npm CLI for package management
- [ ] Terminal/Command Prompt

### Practice Platforms
- [ ] Node.js exercises on various platforms
- [ ] CLI application examples
- [ ] Open source Node.js projects for reference

## 🔄 Challenges & Solutions

### Common Challenges This Week
1. **Understanding Event Loop**
   - Challenge: Predicting async operation order
   - Solution: Practice with timing examples and diagrams

2. **Module System Confusion**
   - Challenge: CommonJS vs ES modules
   - Solution: Create examples using both systems

3. **File Operation Errors**
   - Challenge: Handling file not found, permissions
   - Solution: Comprehensive error handling patterns

4. **Path Manipulation**
   - Challenge: Cross-platform path handling
   - Solution: Always use path module utilities

### Tips for Success
- **Practice Daily**: Write code every day, even small examples
- **Read Documentation**: Node.js docs are excellent resources
- **Experiment**: Try variations of examples to understand concepts
- **Build Projects**: Apply concepts in practical applications

## 🎯 Preparation for Week 2

### Concepts to Review
- [ ] Asynchronous programming patterns
- [ ] HTTP protocol basics
- [ ] Request/response cycle
- [ ] Stream concepts

### Skills to Strengthen
- [ ] Error handling patterns
- [ ] Module organization
- [ ] Testing approaches
- [ ] Documentation writing

### Next Week Preview
Week 2 will focus on HTTP servers and streams. You'll learn to:
- Create HTTP servers with native Node.js
- Handle HTTP requests and responses
- Work with streams for efficient data processing
- Build a static file server

---

**Week 1 Status:** [ ] Complete
**Ready for Week 2:** [ ] Yes / [ ] Need more practice
**Overall Satisfaction:** ___/5
**Time Spent:** ___ hours

**Personal Notes:**
[Add your thoughts about Week 1 learning experience]
