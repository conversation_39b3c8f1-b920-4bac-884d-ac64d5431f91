// Week 4 Day 17: Destructuring Examples
// Date: [Enter today's date]

console.log("=== JavaScript Destructuring Examples ===");

// ===== ARRAY DESTRUCTURING =====
console.log("\n1. Array Destructuring:");

// Basic array destructuring
let colors = ["red", "green", "blue", "yellow"];
let [first, second, third] = colors;

console.log("First color:", first);   // red
console.log("Second color:", second); // green
console.log("Third color:", third);   // blue

// Skipping elements
let [primary, , tertiary] = colors;
console.log("Primary:", primary);     // red
console.log("Tertiary:", tertiary);   // blue

// Rest operator in destructuring
let [head, ...tail] = colors;
console.log("Head:", head);           // red
console.log("Tail:", tail);           // ["green", "blue", "yellow"]

// Default values
let [a, b, c, d, e = "default"] = colors;
console.log("Fifth color:", e);       // yellow (from array)

let [x, y, z, w, v = "default"] = ["one", "two"];
console.log("Fifth value:", v);       // default

// Swapping variables
let var1 = "hello";
let var2 = "world";
console.log("Before swap:", var1, var2);

[var1, var2] = [var2, var1];
console.log("After swap:", var1, var2);

// Nested array destructuring
let nestedArray = [1, [2, 3], 4];
let [num1, [num2, num3], num4] = nestedArray;
console.log("Nested values:", num1, num2, num3, num4);

// ===== OBJECT DESTRUCTURING =====
console.log("\n2. Object Destructuring:");

// Basic object destructuring
let person = {
    name: "John Doe",
    age: 30,
    city: "New York",
    country: "USA"
};

let { name, age, city } = person;
console.log("Name:", name);
console.log("Age:", age);
console.log("City:", city);

// Renaming variables
let { name: fullName, age: years } = person;
console.log("Full name:", fullName);
console.log("Years:", years);

// Default values
let { name: personName, occupation = "Unknown" } = person;
console.log("Person name:", personName);
console.log("Occupation:", occupation);

// Rest operator with objects
let { name: userName, ...otherInfo } = person;
console.log("User name:", userName);
console.log("Other info:", otherInfo);

// Nested object destructuring
let user = {
    id: 1,
    profile: {
        firstName: "Jane",
        lastName: "Smith",
        contact: {
            email: "<EMAIL>",
            phone: "555-0123"
        }
    },
    preferences: {
        theme: "dark",
        language: "en"
    }
};

let {
    id,
    profile: {
        firstName,
        lastName,
        contact: { email, phone }
    },
    preferences: { theme }
} = user;

console.log("User ID:", id);
console.log("First name:", firstName);
console.log("Email:", email);
console.log("Theme:", theme);

// ===== FUNCTION PARAMETER DESTRUCTURING =====
console.log("\n3. Function Parameter Destructuring:");

// Array parameter destructuring
function processCoordinates([x, y, z = 0]) {
    console.log(`Coordinates: x=${x}, y=${y}, z=${z}`);
    return { x, y, z };
}

processCoordinates([10, 20]);
processCoordinates([5, 15, 25]);

// Object parameter destructuring
function createUser({ name, email, age = 18, role = "user" }) {
    console.log(`Creating user: ${name}, ${email}, ${age}, ${role}`);
    return {
        id: Math.random().toString(36).substr(2, 9),
        name,
        email,
        age,
        role,
        createdAt: new Date()
    };
}

let newUser1 = createUser({
    name: "Alice Johnson",
    email: "<EMAIL>"
});

let newUser2 = createUser({
    name: "Bob Wilson",
    email: "<EMAIL>",
    age: 25,
    role: "admin"
});

console.log("New user 1:", newUser1);
console.log("New user 2:", newUser2);

// Mixed destructuring in function parameters
function processOrder({ orderId, items: [firstItem, ...otherItems], customer: { name, email } }) {
    console.log("Order ID:", orderId);
    console.log("First item:", firstItem);
    console.log("Other items:", otherItems);
    console.log("Customer:", name, email);
}

let order = {
    orderId: "ORD-001",
    items: ["laptop", "mouse", "keyboard"],
    customer: {
        name: "John Doe",
        email: "<EMAIL>"
    }
};

processOrder(order);

// ===== DESTRUCTURING WITH LOOPS =====
console.log("\n4. Destructuring with Loops:");

// Array of arrays
let coordinates = [[1, 2], [3, 4], [5, 6]];

console.log("Coordinates:");
for (let [x, y] of coordinates) {
    console.log(`Point: (${x}, ${y})`);
}

// Array of objects
let students = [
    { name: "Alice", grade: 85, subject: "Math" },
    { name: "Bob", grade: 92, subject: "Science" },
    { name: "Carol", grade: 78, subject: "History" }
];

console.log("Student grades:");
for (let { name, grade, subject } of students) {
    console.log(`${name}: ${grade} in ${subject}`);
}

// Object entries
let scores = { math: 95, science: 88, history: 92 };

console.log("Subject scores:");
for (let [subject, score] of Object.entries(scores)) {
    console.log(`${subject}: ${score}`);
}

// ===== PRACTICAL DESTRUCTURING EXAMPLES =====
console.log("\n5. Practical Destructuring Examples:");

// API response handling
function handleApiResponse(response) {
    let {
        data: { users, totalCount },
        status,
        message = "Success"
    } = response;
    
    console.log(`Status: ${status}, Message: ${message}`);
    console.log(`Found ${totalCount} users`);
    
    users.forEach(({ id, name, email }) => {
        console.log(`User ${id}: ${name} (${email})`);
    });
}

let apiResponse = {
    status: 200,
    data: {
        users: [
            { id: 1, name: "Alice", email: "<EMAIL>" },
            { id: 2, name: "Bob", email: "<EMAIL>" }
        ],
        totalCount: 2
    }
};

handleApiResponse(apiResponse);

// Configuration object destructuring
function initializeApp({
    apiUrl = "https://api.example.com",
    timeout = 5000,
    retries = 3,
    features: { auth = true, analytics = false } = {}
} = {}) {
    console.log("App configuration:");
    console.log(`API URL: ${apiUrl}`);
    console.log(`Timeout: ${timeout}ms`);
    console.log(`Retries: ${retries}`);
    console.log(`Auth enabled: ${auth}`);
    console.log(`Analytics enabled: ${analytics}`);
}

// With full config
initializeApp({
    apiUrl: "https://custom-api.com",
    timeout: 10000,
    features: {
        auth: true,
        analytics: true
    }
});

// With partial config
initializeApp({
    timeout: 8000
});

// With no config (all defaults)
initializeApp();

// Form data processing
function processFormData(formData) {
    let {
        personalInfo: { firstName, lastName, email },
        address: { street, city, zipCode, country = "USA" },
        preferences: { newsletter = false, notifications = true }
    } = formData;
    
    console.log("Processing form data:");
    console.log(`Name: ${firstName} ${lastName}`);
    console.log(`Email: ${email}`);
    console.log(`Address: ${street}, ${city}, ${zipCode}, ${country}`);
    console.log(`Newsletter: ${newsletter}, Notifications: ${notifications}`);
}

let formData = {
    personalInfo: {
        firstName: "John",
        lastName: "Doe",
        email: "<EMAIL>"
    },
    address: {
        street: "123 Main St",
        city: "New York",
        zipCode: "10001"
    },
    preferences: {
        newsletter: true
    }
};

processFormData(formData);

// ===== DESTRUCTURING WITH COMPUTED PROPERTIES =====
console.log("\n6. Destructuring with Computed Properties:");

// Dynamic property names
let propertyName = "username";
let userData = { username: "johndoe", email: "<EMAIL>" };

let { [propertyName]: dynamicValue } = userData;
console.log("Dynamic value:", dynamicValue);

// Multiple computed properties
let config = { theme: "dark", language: "en", fontSize: 14 };
let properties = ["theme", "language"];

let extractedConfig = {};
properties.forEach(prop => {
    let { [prop]: value } = config;
    extractedConfig[prop] = value;
});

console.log("Extracted config:", extractedConfig);

// ===== DESTRUCTURING BEST PRACTICES =====
console.log("\n7. Destructuring Best Practices:");

// Clean function returns
function getUserInfo(userId) {
    // Simulate database query
    let userData = {
        id: userId,
        name: "Jane Doe",
        email: "<EMAIL>",
        profile: {
            bio: "Software developer",
            location: "San Francisco"
        },
        stats: {
            posts: 42,
            followers: 150
        }
    };
    
    // Return destructured data
    let {
        name,
        email,
        profile: { bio, location },
        stats: { posts, followers }
    } = userData;
    
    return { name, email, bio, location, posts, followers };
}

let userInfo = getUserInfo(123);
console.log("User info:", userInfo);

// Error handling with destructuring
function parseApiError(error) {
    let {
        response: {
            status = 500,
            data: { message = "Unknown error", code = "UNKNOWN" } = {}
        } = {}
    } = error;
    
    return { status, message, code };
}

// Simulate different error scenarios
let error1 = {
    response: {
        status: 404,
        data: {
            message: "User not found",
            code: "USER_NOT_FOUND"
        }
    }
};

let error2 = {}; // Incomplete error object

console.log("Error 1:", parseApiError(error1));
console.log("Error 2:", parseApiError(error2));

console.log("\n=== Destructuring Examples Complete ===");

// Practice exercises
console.log("\n=== Practice Exercises ===");
console.log("Try these exercises:");
console.log("1. Create a function that destructures a complex nested object");
console.log("2. Use destructuring to swap multiple variables at once");
console.log("3. Build a function that processes array data using destructuring");
console.log("4. Create a configuration system using destructuring with defaults");
