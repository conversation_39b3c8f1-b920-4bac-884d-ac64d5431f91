// Week 3 Day 11: Callback Examples
// Date: [Enter today's date]

console.log("=== JavaScript Callback Examples ===");

// ===== BASIC CALLBACK CONCEPTS =====
console.log("\n1. Basic Callback Concepts:");

// Simple callback function
function greetUser(name, callback) {
    console.log(`Hello, ${name}!`);
    callback();
}

function afterGreeting() {
    console.log("Nice to meet you!");
}

greetUser("Alice", afterGreeting);

// Callback with parameters
function processData(data, callback) {
    console.log("Processing data:", data);
    let result = data.toUpperCase();
    callback(result);
}

function handleResult(result) {
    console.log("Processed result:", result);
}

processData("hello world", handleResult);

// Anonymous callback function
function calculate(a, b, operation) {
    let result = operation(a, b);
    console.log(`Result: ${result}`);
    return result;
}

calculate(5, 3, function(x, y) {
    return x + y;
});

calculate(10, 4, (x, y) => x * y);

// ===== ASYNCHRONOUS CALLBACKS =====
console.log("\n2. Asynchronous Callbacks:");

// setTimeout with callback
console.log("Starting timer...");
setTimeout(function() {
    console.log("Timer finished after 1 second!");
}, 1000);

setTimeout(() => {
    console.log("Arrow function timer finished after 2 seconds!");
}, 2000);

// setInterval with callback
let counter = 0;
let intervalId = setInterval(function() {
    counter++;
    console.log(`Interval tick: ${counter}`);
    
    if (counter >= 3) {
        clearInterval(intervalId);
        console.log("Interval stopped");
    }
}, 500);

// Simulating asynchronous operations
function fetchUserData(userId, callback) {
    console.log(`Fetching user data for ID: ${userId}`);
    
    // Simulate network delay
    setTimeout(() => {
        let userData = {
            id: userId,
            name: "John Doe",
            email: "<EMAIL>"
        };
        callback(null, userData); // null for error, userData for success
    }, 1500);
}

function handleUserData(error, data) {
    if (error) {
        console.error("Error fetching user data:", error);
    } else {
        console.log("User data received:", data);
    }
}

fetchUserData(123, handleUserData);

// ===== CALLBACK PATTERNS =====
console.log("\n3. Common Callback Patterns:");

// Error-first callback pattern (Node.js style)
function readFile(filename, callback) {
    console.log(`Reading file: ${filename}`);
    
    setTimeout(() => {
        if (filename.endsWith('.txt')) {
            callback(null, "File content here...");
        } else {
            callback(new Error("Invalid file type"), null);
        }
    }, 1000);
}

readFile("document.txt", (error, content) => {
    if (error) {
        console.error("File read error:", error.message);
    } else {
        console.log("File content:", content);
    }
});

readFile("image.jpg", (error, content) => {
    if (error) {
        console.error("File read error:", error.message);
    } else {
        console.log("File content:", content);
    }
});

// Multiple callbacks pattern
function processOrder(order, onSuccess, onError) {
    console.log("Processing order:", order.id);
    
    setTimeout(() => {
        if (order.amount > 0) {
            onSuccess({
                orderId: order.id,
                status: "completed",
                total: order.amount
            });
        } else {
            onError(new Error("Invalid order amount"));
        }
    }, 800);
}

let order = { id: "ORD-001", amount: 99.99 };

processOrder(
    order,
    (result) => console.log("Order success:", result),
    (error) => console.error("Order failed:", error.message)
);

// ===== CALLBACK HELL EXAMPLE =====
console.log("\n4. Callback Hell Example:");

// This demonstrates the problem with nested callbacks
function step1(callback) {
    setTimeout(() => {
        console.log("Step 1 completed");
        callback(null, "result1");
    }, 300);
}

function step2(data, callback) {
    setTimeout(() => {
        console.log("Step 2 completed with:", data);
        callback(null, "result2");
    }, 300);
}

function step3(data, callback) {
    setTimeout(() => {
        console.log("Step 3 completed with:", data);
        callback(null, "final result");
    }, 300);
}

// Nested callbacks (callback hell)
step1((error1, result1) => {
    if (error1) {
        console.error("Error in step 1:", error1);
    } else {
        step2(result1, (error2, result2) => {
            if (error2) {
                console.error("Error in step 2:", error2);
            } else {
                step3(result2, (error3, result3) => {
                    if (error3) {
                        console.error("Error in step 3:", error3);
                    } else {
                        console.log("All steps completed:", result3);
                    }
                });
            }
        });
    }
});

// ===== ARRAY METHODS WITH CALLBACKS =====
console.log("\n5. Array Methods with Callbacks:");

let numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];

// forEach with callback
console.log("Using forEach:");
numbers.forEach(function(num, index) {
    console.log(`Index ${index}: ${num}`);
});

// map with callback
console.log("Using map:");
let doubled = numbers.map(num => num * 2);
console.log("Doubled numbers:", doubled);

// filter with callback
console.log("Using filter:");
let evenNumbers = numbers.filter(function(num) {
    return num % 2 === 0;
});
console.log("Even numbers:", evenNumbers);

// reduce with callback
console.log("Using reduce:");
let sum = numbers.reduce((accumulator, current) => {
    return accumulator + current;
}, 0);
console.log("Sum of numbers:", sum);

// find with callback
let found = numbers.find(num => num > 5);
console.log("First number > 5:", found);

// ===== EVENT SIMULATION WITH CALLBACKS =====
console.log("\n6. Event Simulation with Callbacks:");

// Simple event emitter simulation
function EventEmitter() {
    this.events = {};
    
    this.on = function(eventName, callback) {
        if (!this.events[eventName]) {
            this.events[eventName] = [];
        }
        this.events[eventName].push(callback);
    };
    
    this.emit = function(eventName, data) {
        if (this.events[eventName]) {
            this.events[eventName].forEach(callback => {
                callback(data);
            });
        }
    };
}

let emitter = new EventEmitter();

// Register event listeners
emitter.on('userLogin', function(user) {
    console.log(`User logged in: ${user.name}`);
});

emitter.on('userLogin', (user) => {
    console.log(`Welcome back, ${user.name}!`);
});

emitter.on('userLogout', function(user) {
    console.log(`User logged out: ${user.name}`);
});

// Emit events
setTimeout(() => {
    emitter.emit('userLogin', { name: 'Alice', id: 1 });
}, 2000);

setTimeout(() => {
    emitter.emit('userLogout', { name: 'Alice', id: 1 });
}, 4000);

// ===== PRACTICAL CALLBACK EXAMPLES =====
console.log("\n7. Practical Callback Examples:");

// Data validation with callbacks
function validateEmail(email, callback) {
    setTimeout(() => {
        let isValid = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
        callback(isValid);
    }, 100);
}

function validatePassword(password, callback) {
    setTimeout(() => {
        let isValid = password.length >= 8;
        callback(isValid);
    }, 100);
}

function validateForm(formData, callback) {
    validateEmail(formData.email, (emailValid) => {
        if (!emailValid) {
            callback(false, "Invalid email");
            return;
        }
        
        validatePassword(formData.password, (passwordValid) => {
            if (!passwordValid) {
                callback(false, "Password too short");
                return;
            }
            
            callback(true, "Form is valid");
        });
    });
}

let formData = {
    email: "<EMAIL>",
    password: "password123"
};

validateForm(formData, (isValid, message) => {
    console.log(`Form validation: ${isValid ? 'PASSED' : 'FAILED'} - ${message}`);
});

// Database simulation with callbacks
function Database() {
    this.users = [
        { id: 1, name: "Alice", email: "<EMAIL>" },
        { id: 2, name: "Bob", email: "<EMAIL>" }
    ];
    
    this.findUser = function(id, callback) {
        setTimeout(() => {
            let user = this.users.find(u => u.id === id);
            if (user) {
                callback(null, user);
            } else {
                callback(new Error("User not found"), null);
            }
        }, 200);
    };
    
    this.createUser = function(userData, callback) {
        setTimeout(() => {
            let newUser = {
                id: this.users.length + 1,
                ...userData
            };
            this.users.push(newUser);
            callback(null, newUser);
        }, 300);
    };
}

let db = new Database();

db.findUser(1, (error, user) => {
    if (error) {
        console.error("Database error:", error.message);
    } else {
        console.log("Found user:", user);
    }
});

db.createUser({ name: "Charlie", email: "<EMAIL>" }, (error, user) => {
    if (error) {
        console.error("Create user error:", error.message);
    } else {
        console.log("Created user:", user);
    }
});

console.log("\n=== Callback Examples Complete ===");

// Practice exercises
console.log("\n=== Practice Exercises ===");
console.log("Try these exercises:");
console.log("1. Create a callback-based timer that counts down from 10");
console.log("2. Build a callback-based file processing system");
console.log("3. Create a callback-based shopping cart with validation");
console.log("4. Implement a callback-based quiz system with scoring");
