# Phase 1: JavaScript Fundamentals - Detailed Guide

## Overview
Phase 1 is your foundation for becoming a backend developer. You'll master core JavaScript concepts that are essential for Node.js development. This phase typically takes 4-6 weeks of consistent study (2-3 hours daily).

**Learning Objectives:**
- Master JavaScript syntax and core concepts
- Understand asynchronous programming patterns
- Write clean, maintainable JavaScript code
- Handle errors effectively
- Use modern ES6+ features confidently

---

## Week 1: Variables, Data Types & Basic Operations

### Day 1-2: Variables and Data Types

#### Variable Declarations
```javascript
// var - function-scoped, can be redeclared and updated
var name = "<PERSON>";
var name = "<PERSON>"; // This works but is not recommended

// let - block-scoped, can be updated but not redeclared
let age = 25;
age = 26; // This works
// let age = 27; // This would cause an error

// const - block-scoped, cannot be updated or redeclared
const PI = 3.14159;
// PI = 3.14; // This would cause an error
```

#### Primitive Data Types
```javascript
// String
let firstName = "John";
let lastName = 'Doe';
let fullName = `${firstName} ${lastName}`; // Template literal

// Number
let integer = 42;
let decimal = 3.14;
let negative = -10;
let infinity = Infinity;
let notANumber = NaN;

// Boolean
let isActive = true;
let isCompleted = false;

// Undefined
let undefinedVariable;
console.log(undefinedVariable); // undefined

// Null
let emptyValue = null;

// Symbol (ES6)
let sym1 = Symbol('id');
let sym2 = Symbol('id');
console.log(sym1 === sym2); // false

// BigInt (ES2020)
let bigNumber = 1234567890123456789012345678901234567890n;
```

#### Reference Types
```javascript
// Objects
let person = {
    name: "John",
    age: 30,
    isEmployed: true,
    address: {
        street: "123 Main St",
        city: "New York"
    }
};

// Arrays
let numbers = [1, 2, 3, 4, 5];
let mixedArray = [1, "hello", true, null, {name: "John"}];

// Functions
function greet(name) {
    return `Hello, ${name}!`;
}
```

#### Type Checking and Conversion
```javascript
// Type checking
console.log(typeof "hello"); // "string"
console.log(typeof 42); // "number"
console.log(typeof true); // "boolean"
console.log(typeof undefined); // "undefined"
console.log(typeof null); // "object" (this is a known quirk)
console.log(typeof {}); // "object"
console.log(typeof []); // "object"
console.log(Array.isArray([])); // true

// Type conversion
let num = "123";
console.log(Number(num)); // 123
console.log(parseInt(num)); // 123
console.log(parseFloat("123.45")); // 123.45
console.log(String(123)); // "123"
console.log(Boolean(1)); // true
console.log(Boolean(0)); // false
```

#### Practical Exercise Day 1-2
Create a personal information manager that:
1. Stores different types of data about a person
2. Validates data types
3. Converts between different types when needed

### Day 3-4: Operators and Control Structures

#### Operators
```javascript
// Arithmetic operators
let a = 10, b = 3;
console.log(a + b); // Addition: 13
console.log(a - b); // Subtraction: 7
console.log(a * b); // Multiplication: 30
console.log(a / b); // Division: 3.333...
console.log(a % b); // Modulus: 1
console.log(a ** b); // Exponentiation: 1000

// Assignment operators
let x = 5;
x += 3; // x = x + 3; Result: 8
x -= 2; // x = x - 2; Result: 6
x *= 2; // x = x * 2; Result: 12
x /= 3; // x = x / 3; Result: 4

// Comparison operators
console.log(5 == "5"); // true (loose equality)
console.log(5 === "5"); // false (strict equality)
console.log(5 != "5"); // false
console.log(5 !== "5"); // true
console.log(5 > 3); // true
console.log(5 >= 5); // true

// Logical operators
let isLoggedIn = true;
let hasPermission = false;
console.log(isLoggedIn && hasPermission); // false (AND)
console.log(isLoggedIn || hasPermission); // true (OR)
console.log(!isLoggedIn); // false (NOT)

// Ternary operator
let age = 18;
let status = age >= 18 ? "adult" : "minor";
console.log(status); // "adult"

// Nullish coalescing operator (ES2020)
let username = null;
let defaultName = username ?? "Guest";
console.log(defaultName); // "Guest"
```

#### Control Structures
```javascript
// If-else statements
let score = 85;
if (score >= 90) {
    console.log("A grade");
} else if (score >= 80) {
    console.log("B grade");
} else if (score >= 70) {
    console.log("C grade");
} else {
    console.log("Need improvement");
}

// Switch statement
let day = "Monday";
switch (day) {
    case "Monday":
        console.log("Start of work week");
        break;
    case "Friday":
        console.log("TGIF!");
        break;
    case "Saturday":
    case "Sunday":
        console.log("Weekend!");
        break;
    default:
        console.log("Regular day");
}

// Loops
// For loop
for (let i = 0; i < 5; i++) {
    console.log(`Iteration ${i}`);
}

// While loop
let count = 0;
while (count < 3) {
    console.log(`Count: ${count}`);
    count++;
}

// Do-while loop
let num = 0;
do {
    console.log(`Number: ${num}`);
    num++;
} while (num < 3);

// For-of loop (for arrays)
let fruits = ["apple", "banana", "orange"];
for (let fruit of fruits) {
    console.log(fruit);
}

// For-in loop (for objects)
let person = {name: "John", age: 30, city: "New York"};
for (let key in person) {
    console.log(`${key}: ${person[key]}`);
}
```

#### Practical Exercise Day 3-4
Build a simple grade calculator that:
1. Takes multiple test scores
2. Calculates average
3. Assigns letter grades
4. Uses various operators and control structures

---

## Week 2: Functions and Scope

### Day 5-7: Functions Deep Dive

#### Function Declarations vs Expressions
```javascript
// Function Declaration - Hoisted
console.log(add(2, 3)); // This works due to hoisting
function add(a, b) {
    return a + b;
}

// Function Expression - Not hoisted
// console.log(subtract(5, 2)); // This would cause an error
const subtract = function(a, b) {
    return a - b;
};

// Arrow Functions (ES6)
const multiply = (a, b) => a * b;
const divide = (a, b) => {
    if (b === 0) {
        throw new Error("Division by zero");
    }
    return a / b;
};

// Single parameter arrow function
const square = x => x * x;

// No parameters
const greet = () => "Hello!";
```

#### Function Parameters and Arguments
```javascript
// Default parameters
function createUser(name, age = 18, role = "user") {
    return {
        name: name,
        age: age,
        role: role
    };
}

console.log(createUser("John")); // age=18, role="user"
console.log(createUser("Jane", 25, "admin"));

// Rest parameters
function sum(...numbers) {
    return numbers.reduce((total, num) => total + num, 0);
}

console.log(sum(1, 2, 3, 4, 5)); // 15

// Destructuring parameters
function displayUser({name, age, email}) {
    console.log(`Name: ${name}, Age: ${age}, Email: ${email}`);
}

displayUser({name: "John", age: 30, email: "<EMAIL>"});
```

---

## Links to Weekly Activities

- [Week 1 Activities](week-1-fundamentals/WEEK-1-ACTIVITIES.md)
- [Week 2 Activities](week-2-functions-scope/WEEK-2-ACTIVITIES.md)
- [Week 3 Activities](week-3-asynchronous-js/WEEK-3-ACTIVITIES.md)
- [Week 4 Activities](week-4-modern-js-errors/WEEK-4-ACTIVITIES.md)
- [Final Project](final-project/FINAL-PROJECT-REQUIREMENTS.md)

## Assessment Checklist

Before moving to Phase 2, ensure you can:

**Variables and Data Types**
- [ ] Explain the differences between var, let, and const
- [ ] Work with all primitive and reference data types
- [ ] Perform type checking and conversion

**Functions and Scope**
- [ ] Write different types of functions (declaration, expression, arrow)
- [ ] Understand and use closures effectively
- [ ] Explain how 'this' binding works
- [ ] Use higher-order functions confidently

**Asynchronous JavaScript**
- [ ] Handle callbacks and avoid callback hell
- [ ] Work with Promises and Promise methods
- [ ] Use async/await for clean asynchronous code
- [ ] Understand the event loop conceptually

**Modern JavaScript Features**
- [ ] Use destructuring assignment in various scenarios
- [ ] Apply spread and rest operators appropriately
- [ ] Create and use ES6 classes with inheritance
- [ ] Organize code with ES6 modules

**Error Handling**
- [ ] Implement try-catch-finally blocks effectively
- [ ] Create and use custom error classes
- [ ] Handle both synchronous and asynchronous errors
- [ ] Apply error handling best practices

**Final Project**
- [ ] Complete the Personal Task Management System
- [ ] Demonstrate all Phase 1 concepts in a working application
- [ ] Write clean, documented, and maintainable code

Once you can confidently check all these items, you're ready for Phase 2: Node.js Fundamentals!
