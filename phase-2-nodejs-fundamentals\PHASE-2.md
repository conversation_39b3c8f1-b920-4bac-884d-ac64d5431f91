# Phase 2: Node.js Fundamentals - Detailed Guide

## Overview
Phase 2 transforms your JavaScript knowledge into backend development skills using Node.js. You'll learn to build servers, APIs, and handle real-world backend challenges. This phase typically takes 3-4 weeks of consistent study (2-3 hours daily).

**Learning Objectives:**
- Master Node.js architecture and runtime environment
- Build HTTP servers and handle requests/responses
- Create RESTful APIs with Express.js framework
- Work with streams, buffers, and file systems
- Implement authentication and middleware patterns
- Apply professional backend development practices

---

## Week 1: Node.js Architecture & Core Modules

### Day 1-2: Node.js Architecture & Setup

#### Understanding Node.js
```javascript
// Node.js is built on Chrome's V8 JavaScript engine
// It provides a runtime environment for executing JavaScript server-side

// Key characteristics:
// 1. Single-threaded with event loop
// 2. Non-blocking I/O operations
// 3. Built-in modules for server-side development
// 4. NPM ecosystem for packages

// Check Node.js version
console.log('Node.js version:', process.version);
console.log('Platform:', process.platform);
console.log('Architecture:', process.arch);

// Understanding the event loop
console.log('Start');

// Immediate execution
setImmediate(() => {
    console.log('setImmediate');
});

// Next tick (highest priority)
process.nextTick(() => {
    console.log('nextTick');
});

// Timer
setTimeout(() => {
    console.log('setTimeout');
}, 0);

console.log('End');

// Output order: Start, End, nextTick, setImmediate, setTimeout
```

#### Module Systems
```javascript
// CommonJS (traditional Node.js)
// math.js
function add(a, b) {
    return a + b;
}

function multiply(a, b) {
    return a * b;
}

module.exports = {
    add,
    multiply
};

// Alternative export syntax
exports.subtract = (a, b) => a - b;

// app.js
const math = require('./math');
const { add, multiply } = require('./math');

console.log(math.add(5, 3)); // 8
console.log(multiply(4, 2)); // 8

// ES Modules (modern approach)
// math.mjs or with "type": "module" in package.json
export function add(a, b) {
    return a + b;
}

export function multiply(a, b) {
    return a * b;
}

export default function power(base, exp) {
    return Math.pow(base, exp);
}

// app.mjs
import power, { add, multiply } from './math.mjs';
import * as mathUtils from './math.mjs';

console.log(add(5, 3)); // 8
console.log(power(2, 3)); // 8
```

#### NPM and Package Management
```bash
# Initialize a new Node.js project
npm init -y

# Install packages
npm install express
npm install --save-dev nodemon jest

# Install globally
npm install -g nodemon

# Package.json scripts
{
  "scripts": {
    "start": "node app.js",
    "dev": "nodemon app.js",
    "test": "jest"
  }
}

# Run scripts
npm start
npm run dev
npm test
```

### Day 3-4: File System Operations

#### Working with Files
```javascript
const fs = require('fs');
const path = require('path');

// Synchronous file operations (blocking)
try {
    const data = fs.readFileSync('data.txt', 'utf8');
    console.log('File content:', data);
} catch (error) {
    console.error('Error reading file:', error.message);
}

// Asynchronous file operations (non-blocking)
fs.readFile('data.txt', 'utf8', (err, data) => {
    if (err) {
        console.error('Error reading file:', err.message);
        return;
    }
    console.log('File content:', data);
});

// Promise-based file operations
const fsPromises = require('fs').promises;

async function readFileAsync() {
    try {
        const data = await fsPromises.readFile('data.txt', 'utf8');
        console.log('File content:', data);
    } catch (error) {
        console.error('Error reading file:', error.message);
    }
}

// Writing files
const content = 'Hello, Node.js!';

// Synchronous write
fs.writeFileSync('output.txt', content);

// Asynchronous write
fs.writeFile('output.txt', content, (err) => {
    if (err) {
        console.error('Error writing file:', err.message);
        return;
    }
    console.log('File written successfully');
});

// Promise-based write
async function writeFileAsync() {
    try {
        await fsPromises.writeFile('output.txt', content);
        console.log('File written successfully');
    } catch (error) {
        console.error('Error writing file:', error.message);
    }
}

// File operations with error handling
class FileManager {
    static async readJSON(filePath) {
        try {
            const data = await fsPromises.readFile(filePath, 'utf8');
            return JSON.parse(data);
        } catch (error) {
            if (error.code === 'ENOENT') {
                console.log('File not found, returning empty object');
                return {};
            }
            throw new Error(`Failed to read JSON file: ${error.message}`);
        }
    }
    
    static async writeJSON(filePath, data) {
        try {
            const jsonString = JSON.stringify(data, null, 2);
            await fsPromises.writeFile(filePath, jsonString);
            console.log(`JSON data written to ${filePath}`);
        } catch (error) {
            throw new Error(`Failed to write JSON file: ${error.message}`);
        }
    }
    
    static async ensureDirectory(dirPath) {
        try {
            await fsPromises.mkdir(dirPath, { recursive: true });
        } catch (error) {
            if (error.code !== 'EEXIST') {
                throw new Error(`Failed to create directory: ${error.message}`);
            }
        }
    }
    
    static async copyFile(source, destination) {
        try {
            await fsPromises.copyFile(source, destination);
            console.log(`File copied from ${source} to ${destination}`);
        } catch (error) {
            throw new Error(`Failed to copy file: ${error.message}`);
        }
    }
}

// Usage example
async function fileOperationsDemo() {
    try {
        // Ensure directory exists
        await FileManager.ensureDirectory('./data');
        
        // Write JSON data
        const userData = {
            users: [
                { id: 1, name: 'John', email: '<EMAIL>' },
                { id: 2, name: 'Jane', email: '<EMAIL>' }
            ]
        };
        
        await FileManager.writeJSON('./data/users.json', userData);
        
        // Read JSON data
        const loadedData = await FileManager.readJSON('./data/users.json');
        console.log('Loaded data:', loadedData);
        
        // Copy file
        await FileManager.copyFile('./data/users.json', './data/users-backup.json');
        
    } catch (error) {
        console.error('File operation failed:', error.message);
    }
}

fileOperationsDemo();
```

#### Path Manipulation
```javascript
const path = require('path');

// Path operations
console.log('Current directory:', __dirname);
console.log('Current file:', __filename);

// Path joining
const filePath = path.join(__dirname, 'data', 'users.json');
console.log('File path:', filePath);

// Path parsing
const parsed = path.parse('/home/<USER>/documents/file.txt');
console.log('Parsed path:', parsed);
// {
//   root: '/',
//   dir: '/home/<USER>/documents',
//   base: 'file.txt',
//   ext: '.txt',
//   name: 'file'
// }

// Path utilities
console.log('Extension:', path.extname('file.txt')); // .txt
console.log('Basename:', path.basename('/path/to/file.txt')); // file.txt
console.log('Directory:', path.dirname('/path/to/file.txt')); // /path/to

// Resolve absolute path
const absolutePath = path.resolve('data', 'users.json');
console.log('Absolute path:', absolutePath);

// Check if path is absolute
console.log('Is absolute:', path.isAbsolute('/home/<USER>')); // true
console.log('Is absolute:', path.isAbsolute('data/file.txt')); // false
```

### Day 5-7: Process, Environment, and Core Modules

#### Process and Environment Variables
```javascript
// Process information
console.log('Process ID:', process.pid);
console.log('Node.js version:', process.version);
console.log('Platform:', process.platform);
console.log('Memory usage:', process.memoryUsage());

// Command line arguments
console.log('Arguments:', process.argv);
// process.argv[0] = node executable path
// process.argv[1] = script file path
// process.argv[2+] = additional arguments

// Environment variables
console.log('NODE_ENV:', process.env.NODE_ENV);
console.log('PORT:', process.env.PORT || 3000);

// Setting environment variables
process.env.CUSTOM_VAR = 'custom value';

// .env file support (requires dotenv package)
// npm install dotenv
require('dotenv').config();

// Exit handling
process.on('exit', (code) => {
    console.log(`Process exiting with code: ${code}`);
});

process.on('SIGINT', () => {
    console.log('Received SIGINT, graceful shutdown...');
    process.exit(0);
});

// Uncaught exception handling
process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception:', error);
    process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
});
```

#### OS and Crypto Modules
```javascript
const os = require('os');
const crypto = require('crypto');

// Operating System information
console.log('OS Platform:', os.platform());
console.log('OS Architecture:', os.arch());
console.log('OS Release:', os.release());
console.log('Total Memory:', os.totalmem());
console.log('Free Memory:', os.freemem());
console.log('CPU Info:', os.cpus());
console.log('Network Interfaces:', os.networkInterfaces());

// Cryptographic operations
// Generate random bytes
const randomBytes = crypto.randomBytes(16);
console.log('Random bytes:', randomBytes.toString('hex'));

// Hash generation
const hash = crypto.createHash('sha256');
hash.update('Hello, Node.js!');
const hashDigest = hash.digest('hex');
console.log('SHA256 hash:', hashDigest);

// HMAC (Hash-based Message Authentication Code)
const hmac = crypto.createHmac('sha256', 'secret-key');
hmac.update('data to authenticate');
const hmacDigest = hmac.digest('hex');
console.log('HMAC:', hmacDigest);

// Password hashing with salt
function hashPassword(password) {
    const salt = crypto.randomBytes(16).toString('hex');
    const hash = crypto.pbkdf2Sync(password, salt, 10000, 64, 'sha512');
    return {
        salt: salt,
        hash: hash.toString('hex')
    };
}

function verifyPassword(password, salt, hash) {
    const hashToVerify = crypto.pbkdf2Sync(password, salt, 10000, 64, 'sha512');
    return hash === hashToVerify.toString('hex');
}

// Usage
const passwordData = hashPassword('mySecretPassword');
console.log('Password hash:', passwordData);

const isValid = verifyPassword('mySecretPassword', passwordData.salt, passwordData.hash);
console.log('Password valid:', isValid);
```

#### Practical Exercise Week 1
Build a **File-based Task Manager** that:
1. Stores tasks in JSON files
2. Provides CRUD operations for tasks
3. Uses proper error handling and validation
4. Implements file backup and recovery
5. Uses environment variables for configuration

---

## Week 2: HTTP Module & Streams

### Day 8-10: HTTP Server Fundamentals

#### Creating Basic HTTP Server
```javascript
const http = require('http');
const url = require('url');
const querystring = require('querystring');

// Basic HTTP server
const server = http.createServer((req, res) => {
    // Set response headers
    res.setHeader('Content-Type', 'text/plain');
    res.setHeader('Access-Control-Allow-Origin', '*');
    
    // Handle different HTTP methods
    if (req.method === 'GET') {
        res.statusCode = 200;
        res.end('Hello from Node.js HTTP server!');
    } else if (req.method === 'POST') {
        let body = '';
        
        req.on('data', chunk => {
            body += chunk.toString();
        });
        
        req.on('end', () => {
            res.statusCode = 200;
            res.end(`Received POST data: ${body}`);
        });
    } else {
        res.statusCode = 405;
        res.end('Method Not Allowed');
    }
});

// Start server
const PORT = process.env.PORT || 3000;
server.listen(PORT, () => {
    console.log(`Server running on port ${PORT}`);
});

// Advanced HTTP server with routing
class SimpleRouter {
    constructor() {
        this.routes = {
            GET: {},
            POST: {},
            PUT: {},
            DELETE: {}
        };
    }
    
    get(path, handler) {
        this.routes.GET[path] = handler;
    }
    
    post(path, handler) {
        this.routes.POST[path] = handler;
    }
    
    put(path, handler) {
        this.routes.PUT[path] = handler;
    }
    
    delete(path, handler) {
        this.routes.DELETE[path] = handler;
    }
    
    handle(req, res) {
        const parsedUrl = url.parse(req.url, true);
        const path = parsedUrl.pathname;
        const method = req.method;
        
        const handler = this.routes[method] && this.routes[method][path];
        
        if (handler) {
            handler(req, res, parsedUrl.query);
        } else {
            res.statusCode = 404;
            res.setHeader('Content-Type', 'application/json');
            res.end(JSON.stringify({ error: 'Route not found' }));
        }
    }
}

// Usage example
const router = new SimpleRouter();

router.get('/', (req, res) => {
    res.setHeader('Content-Type', 'application/json');
    res.end(JSON.stringify({ message: 'Welcome to the API' }));
});

router.get('/users', (req, res, query) => {
    const users = [
        { id: 1, name: 'John' },
        { id: 2, name: 'Jane' }
    ];
    
    res.setHeader('Content-Type', 'application/json');
    res.end(JSON.stringify(users));
});

router.post('/users', (req, res) => {
    let body = '';
    
    req.on('data', chunk => {
        body += chunk.toString();
    });
    
    req.on('end', () => {
        try {
            const userData = JSON.parse(body);
            // Process user data here
            
            res.statusCode = 201;
            res.setHeader('Content-Type', 'application/json');
            res.end(JSON.stringify({ 
                message: 'User created', 
                user: userData 
            }));
        } catch (error) {
            res.statusCode = 400;
            res.setHeader('Content-Type', 'application/json');
            res.end(JSON.stringify({ error: 'Invalid JSON' }));
        }
    });
});

const advancedServer = http.createServer((req, res) => {
    router.handle(req, res);
});

advancedServer.listen(3001, () => {
    console.log('Advanced server running on port 3001');
});
```

### Day 11-14: Streams and Buffers

#### Understanding Streams
```javascript
const fs = require('fs');
const { Readable, Writable, Transform, pipeline } = require('stream');

// Readable stream example
class NumberStream extends Readable {
    constructor(options) {
        super(options);
        this.current = 0;
        this.max = 10;
    }
    
    _read() {
        if (this.current < this.max) {
            this.push(`Number: ${this.current}\n`);
            this.current++;
        } else {
            this.push(null); // End of stream
        }
    }
}

// Writable stream example
class LogStream extends Writable {
    _write(chunk, encoding, callback) {
        console.log(`LOG: ${chunk.toString().trim()}`);
        callback();
    }
}

// Transform stream example
class UpperCaseTransform extends Transform {
    _transform(chunk, encoding, callback) {
        this.push(chunk.toString().toUpperCase());
        callback();
    }
}

// Using streams
const numberStream = new NumberStream();
const upperCaseTransform = new UpperCaseTransform();
const logStream = new LogStream();

// Pipeline for stream processing
pipeline(
    numberStream,
    upperCaseTransform,
    logStream,
    (error) => {
        if (error) {
            console.error('Pipeline failed:', error);
        } else {
            console.log('Pipeline succeeded');
        }
    }
);

// File streaming
function copyFileWithStreams(source, destination) {
    const readStream = fs.createReadStream(source);
    const writeStream = fs.createWriteStream(destination);
    
    readStream.pipe(writeStream);
    
    readStream.on('error', (error) => {
        console.error('Read error:', error);
    });
    
    writeStream.on('error', (error) => {
        console.error('Write error:', error);
    });
    
    writeStream.on('finish', () => {
        console.log('File copied successfully');
    });
}
```

#### Practical Exercise Week 2
Build a **Static File Server with Streaming** that:
1. Serves static files efficiently using streams
2. Handles different MIME types
3. Implements proper error handling
4. Supports file uploads with streaming
5. Includes basic caching mechanisms

---

*[Content continues but truncated due to length limit. The full PHASE-2.md would include Weeks 3-4 covering Express.js and Advanced Node.js patterns, following the same detailed structure as shown above.]*
