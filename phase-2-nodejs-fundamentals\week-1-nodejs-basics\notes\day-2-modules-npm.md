# Day 2: Module Systems & NPM

## 🎯 Learning Objectives
- Master CommonJS module system
- Understand ES modules (import/export)
- Learn NPM package management
- Configure package.json effectively
- Understand module resolution and caching

## 📦 Module Systems in Node.js

### CommonJS Modules (Traditional)

#### Exporting from Modules
```javascript
// math.js - CommonJS exports
function add(a, b) {
    return a + b;
}

function subtract(a, b) {
    return a - b;
}

function multiply(a, b) {
    return a * b;
}

// Method 1: module.exports object
module.exports = {
    add,
    subtract,
    multiply
};

// Method 2: exports shorthand
exports.divide = (a, b) => {
    if (b === 0) throw new Error('Division by zero');
    return a / b;
};

// Method 3: Single export
module.exports = function calculator(operation, a, b) {
    switch (operation) {
        case 'add': return a + b;
        case 'subtract': return a - b;
        case 'multiply': return a * b;
        case 'divide': return a / b;
        default: throw new Error('Unknown operation');
    }
};
```

#### Importing CommonJS Modules
```javascript
// app.js - CommonJS imports
const math = require('./math');
const { add, subtract } = require('./math');
const calculator = require('./calculator');

// Using imported functions
console.log(math.add(5, 3)); // 8
console.log(add(5, 3)); // 8
console.log(calculator('multiply', 4, 2)); // 8

// Built-in modules
const fs = require('fs');
const path = require('path');
const http = require('http');
```

### ES Modules (Modern)

#### Exporting from ES Modules
```javascript
// math.mjs - ES module exports
export function add(a, b) {
    return a + b;
}

export function subtract(a, b) {
    return a - b;
}

export function multiply(a, b) {
    return a * b;
}

// Default export
export default function divide(a, b) {
    if (b === 0) throw new Error('Division by zero');
    return a / b;
}

// Named exports object
export { add as sum, subtract as diff };

// Re-exports
export { someFunction } from './other-module.mjs';
```

#### Importing ES Modules
```javascript
// app.mjs - ES module imports
import divide, { add, subtract, multiply } from './math.mjs';
import { sum, diff } from './math.mjs';
import * as mathUtils from './math.mjs';

// Using imported functions
console.log(add(5, 3)); // 8
console.log(divide(10, 2)); // 5
console.log(mathUtils.multiply(4, 2)); // 8

// Dynamic imports
async function loadModule() {
    const { add } = await import('./math.mjs');
    return add(1, 2);
}
```

### Enabling ES Modules in Node.js

#### Method 1: .mjs Extension
```javascript
// Use .mjs extension for ES modules
// math.mjs
export const PI = 3.14159;
```

#### Method 2: package.json Configuration
```json
{
  "type": "module",
  "main": "app.js"
}
```

#### Method 3: Mixed Usage
```json
{
  "type": "module",
  "exports": {
    ".": {
      "import": "./lib/index.mjs",
      "require": "./lib/index.cjs"
    }
  }
}
```

## 📋 NPM Package Management

### Package.json Configuration
```json
{
  "name": "my-node-app",
  "version": "1.0.0",
  "description": "A sample Node.js application",
  "main": "app.js",
  "type": "module",
  "scripts": {
    "start": "node app.js",
    "dev": "nodemon app.js",
    "test": "jest",
    "lint": "eslint .",
    "build": "webpack --mode production"
  },
  "keywords": ["nodejs", "javascript", "backend"],
  "author": "Your Name <<EMAIL>>",
  "license": "MIT",
  "dependencies": {
    "express": "^4.18.0",
    "lodash": "~4.17.21"
  },
  "devDependencies": {
    "nodemon": "^2.0.0",
    "jest": "^28.0.0",
    "eslint": "^8.0.0"
  },
  "engines": {
    "node": ">=18.0.0",
    "npm": ">=8.0.0"
  }
}
```

### NPM Commands
```bash
# Initialize new project
npm init
npm init -y  # Skip questions

# Install packages
npm install express
npm install --save express  # Same as above
npm install --save-dev nodemon  # Development dependency
npm install -g nodemon  # Global installation

# Install from package.json
npm install

# Update packages
npm update
npm update express

# Remove packages
npm uninstall express
npm uninstall --save-dev nodemon

# List packages
npm list
npm list --depth=0
npm list -g

# Check for outdated packages
npm outdated

# Audit for vulnerabilities
npm audit
npm audit fix
```

### Semantic Versioning
```json
{
  "dependencies": {
    "express": "4.18.0",      // Exact version
    "lodash": "~4.17.21",     // Patch updates only
    "moment": "^2.29.0",      // Minor updates allowed
    "axios": ">=0.21.0",      // Minimum version
    "uuid": "*"               // Any version (not recommended)
  }
}
```

## 🔍 Module Resolution

### Module Resolution Algorithm
```javascript
// When you require('./math')
// Node.js looks for:
// 1. ./math.js
// 2. ./math.json
// 3. ./math.node
// 4. ./math/package.json (main field)
// 5. ./math/index.js

// When you require('express')
// Node.js looks in:
// 1. node_modules/express
// 2. ../node_modules/express
// 3. ../../node_modules/express
// 4. Global node_modules
```

### Custom Module Paths
```javascript
// Add custom module paths
module.paths.push('/custom/path/to/modules');

// Or use NODE_PATH environment variable
process.env.NODE_PATH = '/custom/path:/another/path';
require('module').Module._initPaths();
```

## 🗂️ Module Organization Patterns

### Single Responsibility Modules
```javascript
// user.js - User model
class User {
    constructor(name, email) {
        this.name = name;
        this.email = email;
    }
    
    validate() {
        return this.name && this.email;
    }
}

module.exports = User;
```

### Utility Modules
```javascript
// utils.js - Utility functions
const crypto = require('crypto');

function generateId() {
    return crypto.randomBytes(16).toString('hex');
}

function formatDate(date) {
    return date.toISOString().split('T')[0];
}

function validateEmail(email) {
    const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return regex.test(email);
}

module.exports = {
    generateId,
    formatDate,
    validateEmail
};
```

### Service Modules
```javascript
// userService.js - Business logic
const User = require('./user');
const { generateId, validateEmail } = require('./utils');

class UserService {
    constructor() {
        this.users = [];
    }
    
    createUser(name, email) {
        if (!validateEmail(email)) {
            throw new Error('Invalid email format');
        }
        
        const user = new User(name, email);
        user.id = generateId();
        this.users.push(user);
        return user;
    }
    
    findUser(id) {
        return this.users.find(user => user.id === id);
    }
}

module.exports = UserService;
```

## 🔄 Module Caching

### Understanding Module Caching
```javascript
// counter.js
let count = 0;

function increment() {
    return ++count;
}

function getCount() {
    return count;
}

module.exports = { increment, getCount };
```

```javascript
// app.js
const counter1 = require('./counter');
const counter2 = require('./counter');

console.log(counter1.increment()); // 1
console.log(counter2.increment()); // 2 (same instance!)
console.log(counter1.getCount()); // 2

// Modules are cached and reused
console.log(counter1 === counter2); // true
```

### Clearing Module Cache
```javascript
// Clear specific module from cache
delete require.cache[require.resolve('./counter')];

// Clear all modules from cache
Object.keys(require.cache).forEach(key => {
    delete require.cache[key];
});
```

## 🛠️ Practical Examples

### Configuration Module
```javascript
// config.js
const path = require('path');

const config = {
    development: {
        port: 3000,
        database: 'dev.db',
        logLevel: 'debug'
    },
    production: {
        port: process.env.PORT || 8080,
        database: process.env.DATABASE_URL,
        logLevel: 'error'
    }
};

const environment = process.env.NODE_ENV || 'development';

module.exports = config[environment];
```

### Logger Module
```javascript
// logger.js
const fs = require('fs');
const path = require('path');

class Logger {
    constructor(logFile = 'app.log') {
        this.logFile = path.join(__dirname, 'logs', logFile);
        this.ensureLogDirectory();
    }
    
    ensureLogDirectory() {
        const logDir = path.dirname(this.logFile);
        if (!fs.existsSync(logDir)) {
            fs.mkdirSync(logDir, { recursive: true });
        }
    }
    
    log(level, message) {
        const timestamp = new Date().toISOString();
        const logEntry = `[${timestamp}] ${level.toUpperCase()}: ${message}\n`;
        
        console.log(logEntry.trim());
        fs.appendFileSync(this.logFile, logEntry);
    }
    
    info(message) { this.log('info', message); }
    error(message) { this.log('error', message); }
    debug(message) { this.log('debug', message); }
}

module.exports = new Logger(); // Singleton pattern
```

### Module Factory Pattern
```javascript
// database.js
function createDatabase(type, options) {
    switch (type) {
        case 'memory':
            return new MemoryDatabase(options);
        case 'file':
            return new FileDatabase(options);
        default:
            throw new Error(`Unknown database type: ${type}`);
    }
}

class MemoryDatabase {
    constructor(options) {
        this.data = {};
        this.options = options;
    }
    
    set(key, value) {
        this.data[key] = value;
    }
    
    get(key) {
        return this.data[key];
    }
}

class FileDatabase {
    constructor(options) {
        this.filename = options.filename;
        this.data = this.loadData();
    }
    
    loadData() {
        try {
            const content = fs.readFileSync(this.filename, 'utf8');
            return JSON.parse(content);
        } catch (error) {
            return {};
        }
    }
    
    save() {
        fs.writeFileSync(this.filename, JSON.stringify(this.data, null, 2));
    }
}

module.exports = createDatabase;
```

## 🎯 Best Practices

### Module Design Principles
1. **Single Responsibility**: Each module should have one clear purpose
2. **Minimal Interface**: Export only what's necessary
3. **No Side Effects**: Avoid global state modifications
4. **Clear Dependencies**: Make dependencies explicit
5. **Error Handling**: Handle errors appropriately

### CommonJS vs ES Modules
```javascript
// Use CommonJS for:
// - Node.js applications
// - Synchronous loading
// - Dynamic requires
// - Older Node.js versions

// Use ES Modules for:
// - Modern applications
// - Tree shaking
// - Static analysis
// - Browser compatibility
```

## 📝 Practice Exercises

### Exercise 1: Create a Math Library
Create a comprehensive math library with multiple modules.

### Exercise 2: Build a Configuration System
Implement environment-based configuration management.

### Exercise 3: Package Management
Practice installing, updating, and managing npm packages.

## 🔗 Additional Resources

- [Node.js Modules Documentation](https://nodejs.org/api/modules.html)
- [ES Modules in Node.js](https://nodejs.org/api/esm.html)
- [NPM Documentation](https://docs.npmjs.com/)

## 🏁 Day 2 Summary

Today you learned:
- ✅ CommonJS module system (require/module.exports)
- ✅ ES modules (import/export)
- ✅ NPM package management
- ✅ Module resolution and caching
- ✅ Module organization patterns

**Next:** Day 3 will cover File System Operations and data persistence.
