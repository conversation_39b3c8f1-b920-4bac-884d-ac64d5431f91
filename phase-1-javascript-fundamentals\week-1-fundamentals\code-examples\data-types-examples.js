// Week 1 Day 1-2: Data Types Examples
// Date: [Enter today's date]

console.log("=== JavaScript Data Types Examples ===");

// ===== PRIMITIVE DATA TYPES =====
console.log("\n1. Primitive Data Types (Stored by Value):");

// String examples
console.log("--- Strings ---");
let singleQuotes = 'Hello World';
let doubleQuotes = "Hello World";
let templateLiteral = `Hello World`;
let multiLine = `This is a
multi-line
string`;

console.log("Single quotes:", singleQuotes);
console.log("Double quotes:", doubleQuotes);
console.log("Template literal:", templateLiteral);
console.log("Multi-line string:", multiLine);

// String methods
let text = "JavaScript";
console.log("Original text:", text);
console.log("Length:", text.length);
console.log("Uppercase:", text.toUpperCase());
console.log("Lowercase:", text.toLowerCase());
console.log("Character at index 0:", text[0]);
console.log("Substring (0,4):", text.substring(0, 4));

// Number examples
console.log("\n--- Numbers ---");
let integer = 42;
let float = 3.14159;
let negative = -100;
let scientific = 2.5e6; // 2.5 * 10^6
let binary = 0b1010; // Binary (10 in decimal)
let octal = 0o12; // Octal (10 in decimal)
let hexadecimal = 0xFF; // Hexadecimal (255 in decimal)

console.log("Integer:", integer);
console.log("Float:", float);
console.log("Negative:", negative);
console.log("Scientific notation:", scientific);
console.log("Binary:", binary);
console.log("Octal:", octal);
console.log("Hexadecimal:", hexadecimal);

// Special number values
console.log("Infinity:", Infinity);
console.log("-Infinity:", -Infinity);
console.log("NaN:", NaN);
console.log("Is NaN a number?", typeof NaN);

// Number methods
let num = 123.456789;
console.log("Original number:", num);
console.log("Fixed to 2 decimals:", num.toFixed(2));
console.log("Precision of 4:", num.toPrecision(4));
console.log("Parsed integer:", parseInt("123.45"));
console.log("Parsed float:", parseFloat("123.45"));

// Boolean examples
console.log("\n--- Booleans ---");
let isTrue = true;
let isFalse = false;
let boolFromComparison = 5 > 3;
let boolFromFunction = Boolean("hello");

console.log("True value:", isTrue);
console.log("False value:", isFalse);
console.log("From comparison (5 > 3):", boolFromComparison);
console.log("Boolean('hello'):", boolFromFunction);

// Falsy values in JavaScript
console.log("Falsy values:");
console.log("Boolean(false):", Boolean(false));
console.log("Boolean(0):", Boolean(0));
console.log("Boolean(''):", Boolean(''));
console.log("Boolean(null):", Boolean(null));
console.log("Boolean(undefined):", Boolean(undefined));
console.log("Boolean(NaN):", Boolean(NaN));

// Undefined and Null
console.log("\n--- Undefined and Null ---");
let undefinedVar;
let nullVar = null;

console.log("Undefined variable:", undefinedVar);
console.log("Null variable:", nullVar);
console.log("typeof undefined:", typeof undefinedVar);
console.log("typeof null:", typeof nullVar); // Returns "object" - this is a known bug!

// Symbol examples
console.log("\n--- Symbols ---");
let sym1 = Symbol();
let sym2 = Symbol('description');
let sym3 = Symbol('description');

console.log("Symbol 1:", sym1);
console.log("Symbol 2:", sym2);
console.log("Symbol 3:", sym3);
console.log("sym2 === sym3:", sym2 === sym3); // false - symbols are always unique

// BigInt examples
console.log("\n--- BigInt ---");
let bigInt1 = 123456789012345678901234567890n;
let bigInt2 = BigInt("123456789012345678901234567890");

console.log("BigInt literal:", bigInt1);
console.log("BigInt constructor:", bigInt2);
console.log("Are they equal?", bigInt1 === bigInt2);

// ===== REFERENCE DATA TYPES =====
console.log("\n2. Reference Data Types (Stored by Reference):");

// Object examples
console.log("--- Objects ---");
let person = {
    name: "John Doe",
    age: 30,
    isEmployed: true,
    address: {
        street: "123 Main St",
        city: "New York",
        zipCode: "10001"
    },
    hobbies: ["reading", "swimming", "coding"]
};

console.log("Person object:", person);
console.log("Person name:", person.name);
console.log("Person age:", person["age"]); // Bracket notation
console.log("Person address:", person.address);
console.log("Person city:", person.address.city);
console.log("Person hobbies:", person.hobbies);

// Adding and modifying object properties
person.email = "<EMAIL>";
person.age = 31;
console.log("After modifications:", person);

// Array examples
console.log("\n--- Arrays ---");
let numbers = [1, 2, 3, 4, 5];
let mixedArray = [1, "hello", true, null, {name: "object"}, [1, 2, 3]];
let emptyArray = [];

console.log("Numbers array:", numbers);
console.log("Mixed array:", mixedArray);
console.log("Empty array:", emptyArray);

// Array methods
console.log("Array length:", numbers.length);
console.log("First element:", numbers[0]);
console.log("Last element:", numbers[numbers.length - 1]);

numbers.push(6); // Add to end
console.log("After push(6):", numbers);

numbers.pop(); // Remove from end
console.log("After pop():", numbers);

numbers.unshift(0); // Add to beginning
console.log("After unshift(0):", numbers);

numbers.shift(); // Remove from beginning
console.log("After shift():", numbers);

// Function examples
console.log("\n--- Functions ---");
function regularFunction(name) {
    return `Hello, ${name}!`;
}

const arrowFunction = (name) => `Hello, ${name}!`;

const functionExpression = function(name) {
    return `Hello, ${name}!`;
};

console.log("Regular function:", regularFunction("Alice"));
console.log("Arrow function:", arrowFunction("Bob"));
console.log("Function expression:", functionExpression("Charlie"));

// ===== TYPE COMPARISON =====
console.log("\n3. Type Comparison Examples:");

// Primitive vs Reference comparison
let a = 5;
let b = 5;
console.log("Primitive comparison (5 === 5):", a === b); // true

let obj1 = {name: "John"};
let obj2 = {name: "John"};
let obj3 = obj1;
console.log("Object comparison (different objects):", obj1 === obj2); // false
console.log("Object comparison (same reference):", obj1 === obj3); // true

// ===== PRACTICAL EXAMPLES =====
console.log("\n4. Practical Examples:");

// Student record system
const student = {
    id: 1001,
    firstName: "Jane",
    lastName: "Smith",
    grades: [85, 92, 78, 96, 88],
    isActive: true,
    courses: ["Math", "Science", "English"],
    contact: {
        email: "<EMAIL>",
        phone: "555-0123"
    }
};

console.log("Student record:", student);
console.log("Student full name:", `${student.firstName} ${student.lastName}`);
console.log("Average grade:", student.grades.reduce((sum, grade) => sum + grade, 0) / student.grades.length);

// Shopping cart system
const shoppingCart = {
    items: [
        {name: "Laptop", price: 999.99, quantity: 1},
        {name: "Mouse", price: 29.99, quantity: 2},
        {name: "Keyboard", price: 79.99, quantity: 1}
    ],
    customer: {
        name: "John Doe",
        email: "<EMAIL>"
    },
    calculateTotal: function() {
        return this.items.reduce((total, item) => total + (item.price * item.quantity), 0);
    }
};

console.log("Shopping cart:", shoppingCart);
console.log("Cart total:", shoppingCart.calculateTotal().toFixed(2));

console.log("\n=== Data Types Examples Complete ===");

// Practice exercises
console.log("\n=== Practice Exercises ===");
console.log("Try these exercises:");
console.log("1. Create a complex object representing a book with nested properties");
console.log("2. Create an array of objects representing a playlist of songs");
console.log("3. Practice accessing nested object properties");
console.log("4. Experiment with array methods like push, pop, shift, unshift");
