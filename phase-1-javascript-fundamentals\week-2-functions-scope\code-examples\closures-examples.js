// Week 2 Day 8: Closures Examples
// Date: [Enter today's date]

console.log("=== JavaScript Closures Examples ===");

// ===== BASIC CLOSURE CONCEPTS =====
console.log("\n1. Basic Closure Concepts:");

// Simple closure example
function outerFunction(x) {
    // This is the outer function's scope
    
    function innerFunction(y) {
        // This inner function has access to outer function's variables
        return x + y;
    }
    
    return innerFunction;
}

let addFive = outerFunction(5);
console.log("addFive(3):", addFive(3)); // 8

// The closure "remembers" the value of x even after outerFunction has finished
let addTen = outerFunction(10);
console.log("addTen(3):", addTen(3)); // 13

// ===== CLOSURES FOR DATA PRIVACY =====
console.log("\n2. Closures for Data Privacy:");

// Creating private variables with closures
function createBankAccount(initialBalance) {
    let balance = initialBalance; // Private variable
    
    return {
        deposit: function(amount) {
            if (amount > 0) {
                balance += amount;
                return `Deposited $${amount}. New balance: $${balance}`;
            }
            return "Invalid deposit amount";
        },
        
        withdraw: function(amount) {
            if (amount > 0 && amount <= balance) {
                balance -= amount;
                return `Withdrew $${amount}. New balance: $${balance}`;
            }
            return "Invalid withdrawal amount or insufficient funds";
        },
        
        getBalance: function() {
            return balance;
        }
    };
}

let account1 = createBankAccount(100);
console.log("Account 1 operations:");
console.log(account1.deposit(50));
console.log(account1.withdraw(30));
console.log("Current balance:", account1.getBalance());

// The balance variable is truly private - cannot be accessed directly
console.log("Direct access to balance:", account1.balance); // undefined

// ===== CLOSURE FACTORY FUNCTIONS =====
console.log("\n3. Closure Factory Functions:");

// Factory function for creating specialized functions
function createMultiplier(multiplier) {
    return function(number) {
        return number * multiplier;
    };
}

let double = createMultiplier(2);
let triple = createMultiplier(3);
let quadruple = createMultiplier(4);

console.log("Using multiplier functions:");
console.log("double(5):", double(5)); // 10
console.log("triple(5):", triple(5)); // 15
console.log("quadruple(5):", quadruple(5)); // 20

// Factory for creating validators
function createValidator(validationRule) {
    return function(value) {
        return validationRule(value);
    };
}

let isPositive = createValidator(x => x > 0);
let isEven = createValidator(x => x % 2 === 0);
let isLongEnough = createValidator(str => str.length >= 8);

console.log("Validation functions:");
console.log("isPositive(5):", isPositive(5)); // true
console.log("isEven(4):", isEven(4)); // true
console.log("isLongEnough('password123'):", isLongEnough('password123')); // true

// ===== MODULE PATTERN WITH CLOSURES =====
console.log("\n4. Module Pattern with Closures:");

// Creating a module with private and public methods
const Calculator = (function() {
    let history = []; // Private variable
    
    function addToHistory(operation, result) {
        history.push({ operation, result, timestamp: new Date() });
    }
    
    return {
        add: function(a, b) {
            let result = a + b;
            addToHistory(`${a} + ${b}`, result);
            return result;
        },
        
        subtract: function(a, b) {
            let result = a - b;
            addToHistory(`${a} - ${b}`, result);
            return result;
        },
        
        multiply: function(a, b) {
            let result = a * b;
            addToHistory(`${a} * ${b}`, result);
            return result;
        },
        
        getHistory: function() {
            return [...history]; // Return copy to prevent external modification
        },
        
        clearHistory: function() {
            history = [];
            return "History cleared";
        }
    };
})();

console.log("Calculator module:");
console.log("5 + 3 =", Calculator.add(5, 3));
console.log("10 - 4 =", Calculator.subtract(10, 4));
console.log("6 * 7 =", Calculator.multiply(6, 7));
console.log("History:", Calculator.getHistory());

// ===== CLOSURES IN LOOPS =====
console.log("\n5. Closures in Loops:");

// Common mistake with closures in loops
console.log("Common closure mistake:");
var functions = [];
for (var i = 0; i < 3; i++) {
    functions[i] = function() {
        return i; // This will always return 3!
    };
}

for (let j = 0; j < functions.length; j++) {
    console.log(`Function ${j} returns:`, functions[j]()); // All return 3
}

// Correct way using closure
console.log("Correct closure in loop:");
var correctFunctions = [];
for (var i = 0; i < 3; i++) {
    correctFunctions[i] = (function(index) {
        return function() {
            return index;
        };
    })(i);
}

for (let j = 0; j < correctFunctions.length; j++) {
    console.log(`Correct function ${j} returns:`, correctFunctions[j]());
}

// Modern solution with let
console.log("Modern solution with let:");
let modernFunctions = [];
for (let i = 0; i < 3; i++) {
    modernFunctions[i] = function() {
        return i; // Works correctly with let
    };
}

for (let j = 0; j < modernFunctions.length; j++) {
    console.log(`Modern function ${j} returns:`, modernFunctions[j]());
}

// ===== PRACTICAL CLOSURE APPLICATIONS =====
console.log("\n6. Practical Closure Applications:");

// Debounce function using closures
function debounce(func, delay) {
    let timeoutId;
    
    return function(...args) {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => func.apply(this, args), delay);
    };
}

// Throttle function using closures
function throttle(func, limit) {
    let inThrottle;
    
    return function(...args) {
        if (!inThrottle) {
            func.apply(this, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// Memoization using closures
function memoize(func) {
    let cache = {};
    
    return function(...args) {
        let key = JSON.stringify(args);
        
        if (key in cache) {
            console.log("Cache hit for:", key);
            return cache[key];
        }
        
        console.log("Computing for:", key);
        let result = func.apply(this, args);
        cache[key] = result;
        return result;
    };
}

// Example usage of memoization
function expensiveCalculation(n) {
    // Simulate expensive operation
    let result = 0;
    for (let i = 0; i < n * 1000000; i++) {
        result += i;
    }
    return result;
}

let memoizedCalculation = memoize(expensiveCalculation);

console.log("Memoization example:");
console.log("First call:", memoizedCalculation(5));
console.log("Second call (cached):", memoizedCalculation(5));

// ===== CLOSURE-BASED COUNTER =====
console.log("\n7. Advanced Counter with Closures:");

function createAdvancedCounter(initialValue = 0, step = 1) {
    let count = initialValue;
    let history = [];
    
    function recordChange(operation, oldValue, newValue) {
        history.push({
            operation,
            oldValue,
            newValue,
            timestamp: new Date().toISOString()
        });
    }
    
    return {
        increment: function() {
            let oldValue = count;
            count += step;
            recordChange('increment', oldValue, count);
            return count;
        },
        
        decrement: function() {
            let oldValue = count;
            count -= step;
            recordChange('decrement', oldValue, count);
            return count;
        },
        
        reset: function() {
            let oldValue = count;
            count = initialValue;
            recordChange('reset', oldValue, count);
            return count;
        },
        
        getValue: function() {
            return count;
        },
        
        getHistory: function() {
            return [...history];
        },
        
        setStep: function(newStep) {
            step = newStep;
            return `Step set to ${step}`;
        }
    };
}

let advancedCounter = createAdvancedCounter(10, 2);
console.log("Advanced counter:");
console.log("Initial value:", advancedCounter.getValue());
console.log("Increment:", advancedCounter.increment());
console.log("Increment:", advancedCounter.increment());
console.log("Decrement:", advancedCounter.decrement());
console.log("Reset:", advancedCounter.reset());
console.log("History:", advancedCounter.getHistory());

// ===== CLOSURE-BASED STATE MANAGEMENT =====
console.log("\n8. Closure-based State Management:");

function createStateManager(initialState = {}) {
    let state = { ...initialState };
    let subscribers = [];
    
    function notifySubscribers() {
        subscribers.forEach(callback => callback(state));
    }
    
    return {
        getState: function() {
            return { ...state }; // Return copy to prevent direct mutation
        },
        
        setState: function(updates) {
            let oldState = { ...state };
            state = { ...state, ...updates };
            console.log("State updated:", { oldState, newState: state });
            notifySubscribers();
        },
        
        subscribe: function(callback) {
            subscribers.push(callback);
            return function unsubscribe() {
                let index = subscribers.indexOf(callback);
                if (index > -1) {
                    subscribers.splice(index, 1);
                }
            };
        },
        
        reset: function() {
            state = { ...initialState };
            notifySubscribers();
        }
    };
}

let stateManager = createStateManager({ count: 0, user: null });

// Subscribe to state changes
let unsubscribe = stateManager.subscribe((newState) => {
    console.log("State changed:", newState);
});

console.log("State management:");
console.log("Initial state:", stateManager.getState());
stateManager.setState({ count: 5 });
stateManager.setState({ user: "John Doe" });
stateManager.setState({ count: 10, user: "Jane Smith" });

console.log("\n=== Closures Examples Complete ===");

// Practice exercises
console.log("\n=== Practice Exercises ===");
console.log("Try these exercises:");
console.log("1. Create a closure-based shopping cart with private items array");
console.log("2. Build a closure-based timer with start, stop, and reset functionality");
console.log("3. Create a closure-based cache with size limits and expiration");
console.log("4. Implement a closure-based event emitter pattern");
