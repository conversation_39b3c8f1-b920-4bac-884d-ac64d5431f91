# Day 1: Node.js Architecture & Event Loop

## 🎯 Learning Objectives
- Understand Node.js runtime environment and architecture
- Master the event loop and its phases
- Distinguish between blocking and non-blocking operations
- Learn about Node.js process and thread model

## 📚 Node.js Architecture Overview

### What is Node.js?
Node.js is a JavaScript runtime built on Chrome's V8 JavaScript engine. It allows you to run JavaScript on the server side, outside of a web browser.

**Key Characteristics:**
- **Single-threaded** with event loop
- **Non-blocking I/O** operations
- **Event-driven** architecture
- **Cross-platform** (Windows, macOS, Linux)
- **Built-in modules** for server-side development

### Node.js vs Browser JavaScript
```javascript
// Browser JavaScript
console.log('Running in browser');
window.alert('Hello World');
document.getElementById('myElement');

// Node.js JavaScript
console.log('Running in Node.js');
const fs = require('fs'); // File system access
const http = require('http'); // HTTP server creation
process.exit(0); // Process control
```

## 🔄 The Event Loop

### Event Loop Phases
The Node.js event loop has six main phases:

1. **Timer Phase**: Executes callbacks scheduled by `setTimeout()` and `setInterval()`
2. **Pending Callbacks Phase**: Executes I/O callbacks deferred to the next loop iteration
3. **Idle, Prepare Phase**: Only used internally
4. **Poll Phase**: Fetches new I/O events; executes I/O related callbacks
5. **Check Phase**: Executes `setImmediate()` callbacks
6. **Close Callbacks Phase**: Executes close callbacks (e.g., `socket.on('close', ...)`)

### Event Loop Example
```javascript
console.log('Start');

// Timer phase
setTimeout(() => console.log('Timer 1'), 0);
setTimeout(() => console.log('Timer 2'), 0);

// Check phase
setImmediate(() => console.log('Immediate 1'));
setImmediate(() => console.log('Immediate 2'));

// Microtasks (highest priority)
process.nextTick(() => console.log('NextTick 1'));
process.nextTick(() => console.log('NextTick 2'));

Promise.resolve().then(() => console.log('Promise 1'));
Promise.resolve().then(() => console.log('Promise 2'));

console.log('End');

// Output order:
// Start
// End
// NextTick 1
// NextTick 2
// Promise 1
// Promise 2
// Immediate 1
// Immediate 2
// Timer 1
// Timer 2
```

### Microtasks vs Macrotasks
- **Microtasks**: `process.nextTick()`, `Promise.then()`, `queueMicrotask()`
- **Macrotasks**: `setTimeout()`, `setInterval()`, `setImmediate()`, I/O operations

Microtasks always have higher priority and execute before macrotasks.

## ⚡ Blocking vs Non-blocking Operations

### Blocking Operations (Synchronous)
```javascript
const fs = require('fs');

console.log('Before file read');

// This blocks the event loop
try {
    const data = fs.readFileSync('large-file.txt', 'utf8');
    console.log('File read complete');
} catch (error) {
    console.error('Error reading file:', error.message);
}

console.log('After file read');
```

### Non-blocking Operations (Asynchronous)
```javascript
const fs = require('fs');

console.log('Before file read');

// This doesn't block the event loop
fs.readFile('large-file.txt', 'utf8', (err, data) => {
    if (err) {
        console.error('Error reading file:', err.message);
        return;
    }
    console.log('File read complete');
});

console.log('After file read');
```

### Promise-based Non-blocking
```javascript
const fs = require('fs').promises;

console.log('Before file read');

fs.readFile('large-file.txt', 'utf8')
    .then(data => {
        console.log('File read complete');
    })
    .catch(error => {
        console.error('Error reading file:', error.message);
    });

console.log('After file read');
```

### Async/Await Non-blocking
```javascript
const fs = require('fs').promises;

async function readFileAsync() {
    console.log('Before file read');
    
    try {
        const data = await fs.readFile('large-file.txt', 'utf8');
        console.log('File read complete');
    } catch (error) {
        console.error('Error reading file:', error.message);
    }
    
    console.log('After file read');
}

readFileAsync();
```

## 🧵 Process and Thread Model

### Single-threaded Event Loop
```javascript
// Main thread (event loop)
console.log('Main thread operation');

// This runs in the main thread
for (let i = 0; i < 1000000; i++) {
    // CPU-intensive work blocks the event loop
}

console.log('CPU work complete');
```

### Thread Pool for I/O Operations
```javascript
const fs = require('fs');

// These operations use the thread pool
fs.readFile('file1.txt', callback1);
fs.readFile('file2.txt', callback2);
fs.readFile('file3.txt', callback3);
fs.readFile('file4.txt', callback4);

// Thread pool size can be configured
process.env.UV_THREADPOOL_SIZE = 8;
```

## 📊 Performance Considerations

### Event Loop Monitoring
```javascript
// Monitor event loop lag
function measureEventLoopLag() {
    const start = process.hrtime.bigint();
    
    setImmediate(() => {
        const lag = process.hrtime.bigint() - start;
        console.log(`Event loop lag: ${Number(lag) / 1000000}ms`);
    });
}

// Measure lag periodically
setInterval(measureEventLoopLag, 1000);
```

### CPU-intensive Task Handling
```javascript
// Bad: Blocks event loop
function badCpuTask() {
    let result = 0;
    for (let i = 0; i < 10000000; i++) {
        result += i;
    }
    return result;
}

// Better: Break into chunks
function goodCpuTask(callback) {
    let result = 0;
    let i = 0;
    
    function processChunk() {
        const chunkSize = 100000;
        const end = Math.min(i + chunkSize, 10000000);
        
        for (; i < end; i++) {
            result += i;
        }
        
        if (i < 10000000) {
            setImmediate(processChunk);
        } else {
            callback(result);
        }
    }
    
    processChunk();
}
```

## 🔧 Practical Examples

### Event Loop Visualization
```javascript
function demonstrateEventLoop() {
    console.log('=== Event Loop Demo ===');
    
    // Synchronous
    console.log('1. Synchronous operation');
    
    // Timer (macrotask)
    setTimeout(() => console.log('4. Timer callback'), 0);
    
    // Immediate (macrotask)
    setImmediate(() => console.log('5. Immediate callback'));
    
    // NextTick (microtask)
    process.nextTick(() => console.log('3. NextTick callback'));
    
    // Promise (microtask)
    Promise.resolve().then(() => console.log('3. Promise callback'));
    
    // Synchronous
    console.log('2. Another synchronous operation');
}

demonstrateEventLoop();
```

### I/O Operation Timing
```javascript
const fs = require('fs');

function demonstrateIOTiming() {
    console.log('Starting I/O operations...');
    
    const startTime = Date.now();
    
    // Multiple async I/O operations
    fs.readFile(__filename, () => {
        console.log(`File 1 read after ${Date.now() - startTime}ms`);
    });
    
    fs.readFile(__filename, () => {
        console.log(`File 2 read after ${Date.now() - startTime}ms`);
    });
    
    fs.readFile(__filename, () => {
        console.log(`File 3 read after ${Date.now() - startTime}ms`);
    });
    
    console.log('I/O operations initiated');
}

demonstrateIOTiming();
```

## 🎯 Key Takeaways

1. **Single-threaded Event Loop**: Node.js uses a single thread for JavaScript execution
2. **Non-blocking I/O**: I/O operations don't block the main thread
3. **Event-driven**: Applications respond to events asynchronously
4. **Microtask Priority**: `process.nextTick()` and Promises have highest priority
5. **Avoid Blocking**: Never block the event loop with CPU-intensive tasks
6. **Thread Pool**: I/O operations use a separate thread pool

## 📝 Practice Exercises

### Exercise 1: Event Loop Order
Predict the output order of this code:
```javascript
console.log('A');
setTimeout(() => console.log('B'), 0);
process.nextTick(() => console.log('C'));
Promise.resolve().then(() => console.log('D'));
console.log('E');
```

### Exercise 2: Blocking vs Non-blocking
Compare the performance of synchronous vs asynchronous file operations.

### Exercise 3: Event Loop Lag
Create a function that measures and reports event loop lag.

## 🔗 Additional Resources

- [Node.js Event Loop Documentation](https://nodejs.org/en/docs/guides/event-loop-timers-and-nexttick/)
- [Understanding the Node.js Event Loop](https://blog.insiderattack.net/event-loop-and-the-big-picture-nodejs-event-loop-part-1-1cb67a182810)
- [Node.js Architecture Overview](https://nodejs.org/en/docs/guides/blocking-vs-non-blocking/)

## 🏁 Day 1 Summary

Today you learned:
- ✅ Node.js architecture and runtime environment
- ✅ Event loop phases and execution order
- ✅ Difference between blocking and non-blocking operations
- ✅ Process and thread model in Node.js
- ✅ Performance considerations for event loop

**Next:** Day 2 will cover Module Systems & NPM package management.
