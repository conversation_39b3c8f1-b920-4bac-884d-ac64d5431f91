{"name": "phase-2-nodejs-fundamentals", "version": "1.0.0", "description": "Node.js fundamentals learning journey", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint .", "lint:fix": "eslint . --fix", "docs": "swagger-jsdoc -d swaggerDef.js -o swagger.json ./routes/*.js", "setup": "node utilities/setup-scripts/project-initializer.js", "create-app": "node utilities/setup-scripts/create-express-app.js", "generate-routes": "node utilities/setup-scripts/generate-routes.js", "test-api": "node utilities/tools/api-tester.js", "monitor": "node utilities/tools/performance-monitor.js", "analyze-logs": "node utilities/tools/log-analyzer.js", "check-deps": "node utilities/tools/dependency-checker.js"}, "keywords": ["nodejs", "backend", "api", "express", "learning", "fundamentals", "javascript", "server", "http", "streams", "authentication", "middleware"], "author": "Your Name", "license": "MIT", "dependencies": {"express": "^4.18.0", "cors": "^2.8.5", "helmet": "^6.0.0", "morgan": "^1.10.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^8.5.1", "joi": "^17.6.0", "dotenv": "^16.0.0", "multer": "^1.4.5", "compression": "^1.7.4", "express-rate-limit": "^6.6.0", "express-validator": "^6.14.0", "winston": "^3.8.0", "uuid": "^9.0.0"}, "devDependencies": {"nodemon": "^2.0.0", "jest": "^28.0.0", "supertest": "^6.2.0", "eslint": "^8.0.0", "eslint-config-node": "^4.1.0", "eslint-plugin-node": "^11.1.0", "@types/jest": "^28.1.0", "swagger-jsdoc": "^6.2.0", "swagger-ui-express": "^4.5.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/yourusername/phase-2-nodejs-fundamentals.git"}, "bugs": {"url": "https://github.com/yourusername/phase-2-nodejs-fundamentals/issues"}, "homepage": "https://github.com/yourusername/phase-2-nodejs-fundamentals#readme"}