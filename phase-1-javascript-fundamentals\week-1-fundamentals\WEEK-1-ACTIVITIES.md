# Week 1: Variables, Data Types & Control Structures - Learning Activities

## Learning Objectives
By the end of this week, you will:
- [ ] Master variable declarations (var, let, const) and their differences
- [ ] Understand all JavaScript data types (primitives and objects)
- [ ] Use operators effectively in various contexts
- [ ] Implement control structures (if/else, loops, switch)
- [ ] Build a functional calculator application

## Daily Breakdown

### Day 1: Variables and Data Types
**Duration:** 2-3 hours
**Focus:** Variable declarations and primitive data types

#### Morning Session (1 hour)
- [ ] Read: [notes/day-1-variables-types.md](notes/day-1-variables-types.md)
- [ ] Watch: [JavaScript Variables Explained](https://www.youtube.com/watch?v=9WIJQDvt4Us)
- [ ] Run: [code-examples/variables-demo.js](code-examples/variables-demo.js)
- [ ] Run: [code-examples/data-types-examples.js](code-examples/data-types-examples.js)

#### Evening Session (1.5 hours)
- [ ] Complete: [exercises/personal-info-manager.js](exercises/personal-info-manager.js)
- [ ] Practice: Create 5 different variable examples
- [ ] Update: [WEEK-1-PROGRESS.md](WEEK-1-PROGRESS.md)

#### Success Criteria
- [ ] Can explain difference between var, let, and const
- [ ] Can identify all primitive data types
- [ ] Understands type coercion basics
- [ ] Code runs without errors

### Day 2: More Data Types and Type Conversion
**Duration:** 2-3 hours
**Focus:** Objects, arrays, and type conversion

#### Morning Session (1 hour)
- [ ] Read: [notes/day-2-objects-arrays.md](notes/day-2-objects-arrays.md)
- [ ] Run: [code-examples/objects-arrays-demo.js](code-examples/objects-arrays-demo.js)
- [ ] Experiment: Create nested objects and arrays

#### Evening Session (1.5 hours)
- [ ] Complete: [exercises/type-conversion-practice.js](exercises/type-conversion-practice.js)
- [ ] Practice: Build a student record system
- [ ] Update: Progress tracking

#### Success Criteria
- [ ] Can create and manipulate objects
- [ ] Can work with arrays effectively
- [ ] Understands type conversion methods
- [ ] Can access nested object properties

### Day 3: Operators and Expressions
**Duration:** 2-3 hours
**Focus:** All JavaScript operators

#### Morning Session (1 hour)
- [ ] Read: [notes/day-3-operators.md](notes/day-3-operators.md)
- [ ] Run: [code-examples/operators-practice.js](code-examples/operators-practice.js)
- [ ] Practice: Arithmetic and comparison operators

#### Evening Session (1.5 hours)
- [ ] Complete: [exercises/basic-calculator.js](exercises/basic-calculator.js)
- [ ] Practice: Logical operators and ternary operator
- [ ] Update: Progress tracking

#### Success Criteria
- [ ] Can use all arithmetic operators
- [ ] Understands operator precedence
- [ ] Can use logical operators effectively
- [ ] Comfortable with ternary operator

### Day 4: Control Structures
**Duration:** 2-3 hours
**Focus:** If/else, switch, and loops

#### Morning Session (1 hour)
- [ ] Read: [notes/day-4-control-structures.md](notes/day-4-control-structures.md)
- [ ] Run: [code-examples/control-structures.js](code-examples/control-structures.js)
- [ ] Practice: Different types of loops

#### Evening Session (1.5 hours)
- [ ] Complete: [exercises/grade-calculator.js](exercises/grade-calculator.js)
- [ ] Practice: Nested loops and complex conditions
- [ ] Update: Progress tracking

#### Success Criteria
- [ ] Can write effective if/else statements
- [ ] Can use switch statements appropriately
- [ ] Can implement all types of loops
- [ ] Understands when to use each control structure

### Day 5: Week 1 Mini Project
**Duration:** 3-4 hours
**Focus:** Combining all Week 1 concepts

#### Morning Session (2 hours)
- [ ] Plan: [projects/week-1-mini-project/README.md](projects/week-1-mini-project/README.md)
- [ ] Start: Building the Personal Information Manager
- [ ] Implement: Basic data storage and retrieval

#### Evening Session (2 hours)
- [ ] Complete: All project features
- [ ] Test: [projects/week-1-mini-project/test-cases.js](projects/week-1-mini-project/test-cases.js)
- [ ] Document: Add comments and documentation
- [ ] Update: Final progress tracking

#### Success Criteria
- [ ] Project includes all Week 1 concepts
- [ ] Code is clean and well-commented
- [ ] All test cases pass
- [ ] Can explain every line of code

## Week Summary Checklist
- [ ] All daily activities completed
- [ ] Weekly project finished and tested
- [ ] Self-assessment completed
- [ ] Notes organized and reviewed
- [ ] Ready for Week 2 concepts

## Resources Used This Week
- [ ] MDN Web Docs - JavaScript Basics
- [ ] JavaScript.info - Variables and Data Types
- [ ] freeCodeCamp - Basic JavaScript
- [ ] YouTube tutorials (list specific ones you found helpful)

## Challenges & Solutions
Document any challenges you faced and how you overcame them:

### Challenge 1: Understanding Variable Hoisting
- **Problem**: [Describe the issue]
- **Solution**: [How you resolved it]
- **Key Learning**: [What you learned]

### Challenge 2: Type Coercion Confusion
- **Problem**: [Describe the issue]
- **Solution**: [How you resolved it]
- **Key Learning**: [What you learned]

### Challenge 3: Loop Logic Errors
- **Problem**: [Describe the issue]
- **Solution**: [How you resolved it]
- **Key Learning**: [What you learned]

## Code Quality Checklist
Before moving to Week 2, ensure your code:
- [ ] Uses meaningful variable names
- [ ] Has proper indentation and formatting
- [ ] Includes helpful comments
- [ ] Handles edge cases appropriately
- [ ] Follows JavaScript best practices

## Preparation for Week 2
- [ ] Review any weak areas from Week 1
- [ ] Read introduction to functions
- [ ] Set up Week 2 folder structure
- [ ] Plan study schedule for next week

## Self-Reflection Questions
1. What concept did you find most challenging this week?
2. What concept clicked the easiest for you?
3. How has your understanding of JavaScript improved?
4. What would you do differently if starting Week 1 again?
5. What are you most excited to learn in Week 2?

## Week 1 Portfolio Items
Add these completed items to your portfolio:
- [ ] Personal Information Manager project
- [ ] Best code examples from exercises
- [ ] Screenshots of working applications
- [ ] Reflection notes on learning process

---

**Congratulations on completing Week 1!** 🎉

You've built a solid foundation in JavaScript fundamentals. Week 2 will introduce you to the power of functions and scope - get ready to level up your programming skills!
