# CLI Task Manager - Week 1 Project

## 🎯 Project Overview
Build a command-line task management application that demonstrates all Week 1 Node.js concepts including architecture, modules, file operations, and core modules.

## 📚 Learning Objectives
This project demonstrates mastery of:
- ✅ Node.js architecture and event loop
- ✅ CommonJS module system
- ✅ File system operations (async/sync)
- ✅ Core modules (fs, path, crypto, os, process)
- ✅ Environment variables and configuration
- ✅ Error handling and validation
- ✅ CLI application development

## 🏗️ Project Structure
```
task-manager-cli/
├── README.md                # This file
├── package.json            # Project configuration
├── .env.example           # Environment variables template
├── cli.js                 # Main CLI entry point
├── src/
│   ├── models/
│   │   └── Task.js        # Task model class
│   ├── services/
│   │   ├── TaskService.js # Task business logic
│   │   └── FileService.js # File operations
│   ├── utils/
│   │   ├── config.js      # Configuration management
│   │   ├── logger.js      # Logging utility
│   │   ├── validators.js  # Input validation
│   │   └── helpers.js     # Helper functions
│   └── cli/
│       ├── commands.js    # CLI command handlers
│       └── parser.js      # Command line parser
├── data/
│   ├── tasks.json        # Task data storage
│   └── backup/           # Backup files
├── logs/
│   └── app.log          # Application logs
└── tests/
    └── basic-tests.js   # Basic functionality tests
```

## 🚀 Features

### Core Features
- **Task Management**: Create, read, update, delete tasks
- **Task Properties**: Title, description, priority, due date, status
- **Data Persistence**: JSON file-based storage
- **Backup System**: Automatic backup before modifications
- **Search & Filter**: Find tasks by various criteria
- **Statistics**: Task completion statistics

### CLI Commands
```bash
# Task Management
node cli.js add "Buy groceries" --priority high --due "2024-01-15"
node cli.js list
node cli.js list --status pending
node cli.js list --priority high
node cli.js show 1
node cli.js update 1 --status completed
node cli.js delete 1

# Utility Commands
node cli.js stats
node cli.js backup
node cli.js restore <backup-file>
node cli.js search "groceries"
node cli.js cleanup --days 30

# System Commands
node cli.js config
node cli.js help
node cli.js version
```

### Advanced Features
- **Configuration Management**: Environment-based settings
- **Logging System**: Structured application logging
- **Error Handling**: Comprehensive error management
- **Input Validation**: Robust data validation
- **Backup Management**: Automatic and manual backups
- **Cross-platform**: Works on Windows, macOS, Linux

## 🛠️ Technical Implementation

### Node.js Concepts Demonstrated

#### Week 1 Day 1: Node.js Architecture
- **Event Loop**: Async file operations don't block CLI
- **Process Management**: Graceful shutdown handling
- **Memory Management**: Efficient data processing

#### Week 1 Day 2: Module Systems
- **CommonJS Modules**: Clean module organization
- **Module Exports**: Proper export patterns
- **Module Caching**: Efficient module reuse

#### Week 1 Day 3: File System
- **Async File Operations**: Non-blocking file I/O
- **JSON Handling**: Task data serialization
- **Error Handling**: Robust file operation errors

#### Week 1 Day 4: Core Modules
- **Path Module**: Cross-platform path handling
- **Crypto Module**: Unique task ID generation
- **OS Module**: System information display

#### Week 1 Day 5: Process & Environment
- **Environment Variables**: Configuration management
- **Command Line Args**: CLI argument parsing
- **Process Signals**: Graceful shutdown

## 📋 Installation & Setup

### Prerequisites
- Node.js 18+ installed
- Terminal/Command Prompt access
- Text editor for configuration

### Installation Steps
```bash
# Navigate to project directory
cd week-1-nodejs-basics/projects/task-manager-cli

# Install dependencies (if any)
npm install

# Copy environment template
cp .env.example .env

# Make CLI executable (Unix systems)
chmod +x cli.js

# Test installation
node cli.js help
```

### Configuration
Edit `.env` file for custom settings:
```bash
# Data storage location
DATA_DIR=./data

# Backup settings
BACKUP_DIR=./data/backup
AUTO_BACKUP=true
MAX_BACKUPS=10

# Logging settings
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# Task settings
DEFAULT_PRIORITY=medium
DATE_FORMAT=YYYY-MM-DD
```

## 🎮 Usage Examples

### Basic Task Management
```bash
# Add a new task
node cli.js add "Complete Node.js project" --priority high --due "2024-01-20"

# List all tasks
node cli.js list

# List only pending tasks
node cli.js list --status pending

# Show specific task details
node cli.js show 1

# Update task status
node cli.js update 1 --status completed

# Delete a task
node cli.js delete 1
```

### Advanced Operations
```bash
# Search for tasks
node cli.js search "project"

# Get task statistics
node cli.js stats

# Create manual backup
node cli.js backup

# Restore from backup
node cli.js restore backup-2024-01-15-10-30-00.json

# Clean up old completed tasks
node cli.js cleanup --days 30

# Show configuration
node cli.js config

# Show system information
node cli.js info
```

## 🧪 Testing

### Manual Testing
```bash
# Test basic functionality
node cli.js add "Test task 1"
node cli.js add "Test task 2" --priority high
node cli.js list
node cli.js update 1 --status completed
node cli.js stats
node cli.js delete 2

# Test error handling
node cli.js show 999  # Non-existent task
node cli.js update 999 --status completed  # Invalid task
node cli.js add ""  # Empty title
```

### Automated Testing
```bash
# Run basic tests
node tests/basic-tests.js
```

## 🔧 Code Quality Features

### Error Handling
- **File Operation Errors**: Graceful handling of file system errors
- **Validation Errors**: Clear error messages for invalid input
- **System Errors**: Proper handling of system-level errors
- **User Errors**: Helpful error messages for user mistakes

### Input Validation
- **Task Title**: Required, non-empty, reasonable length
- **Priority Levels**: Valid priority values (low, medium, high)
- **Due Dates**: Valid date format and future dates
- **Status Values**: Valid status transitions

### Performance Optimization
- **Lazy Loading**: Load data only when needed
- **Efficient Search**: Optimized search algorithms
- **Memory Management**: Proper cleanup and garbage collection
- **File I/O**: Async operations for responsiveness

### Security Considerations
- **Input Sanitization**: Clean user input
- **File Path Validation**: Prevent directory traversal
- **Error Information**: Don't expose sensitive system info
- **Data Validation**: Validate all data before processing

## 📊 Project Assessment Criteria

### Functionality (40%)
- [ ] All CLI commands work correctly
- [ ] Task CRUD operations function properly
- [ ] Data persistence works reliably
- [ ] Backup and restore features work
- [ ] Search and filtering work correctly

### Code Quality (30%)
- [ ] Clean, readable code structure
- [ ] Proper use of Node.js modules
- [ ] Consistent naming and formatting
- [ ] Comprehensive error handling
- [ ] Well-organized project structure

### Node.js Concepts (20%)
- [ ] Demonstrates event loop understanding
- [ ] Uses async file operations properly
- [ ] Implements proper module organization
- [ ] Uses core modules effectively
- [ ] Handles environment variables correctly

### Documentation (10%)
- [ ] Clear README with setup instructions
- [ ] Code comments explain complex logic
- [ ] Usage examples are provided
- [ ] Error messages are helpful
- [ ] Configuration is documented

## 🎯 Extension Ideas

### Additional Features to Implement
- **Task Categories**: Organize tasks into categories
- **Task Dependencies**: Link related tasks
- **Recurring Tasks**: Support for repeating tasks
- **Time Tracking**: Track time spent on tasks
- **Export/Import**: Export tasks to different formats
- **Reminders**: Task due date notifications
- **Collaboration**: Share tasks between users
- **Sync**: Synchronize with cloud storage

### Technical Enhancements
- **Database Integration**: Replace JSON with SQLite
- **Web Interface**: Add web-based interface
- **API Server**: Create REST API endpoints
- **Real-time Updates**: WebSocket notifications
- **Mobile App**: React Native mobile client
- **Desktop App**: Electron desktop application

## 🏆 Success Indicators

### Technical Mastery
- Application runs without crashes
- All features work as specified
- Code follows Node.js best practices
- Error handling prevents data loss
- Performance is acceptable for CLI use

### Learning Objectives Met
- Demonstrates understanding of Node.js architecture
- Shows proficiency with core modules
- Implements proper async programming patterns
- Uses environment variables effectively
- Organizes code into logical modules

### Professional Quality
- Code is production-ready quality
- Documentation is comprehensive
- Error messages are user-friendly
- Application is easy to install and use
- Project structure follows conventions

---

**Congratulations!** This project represents a significant milestone in your Node.js learning journey. You've built a complete application using core Node.js concepts and are ready to move on to HTTP servers and Express.js in Week 2.

**Next Steps:** Use this project as a foundation for more advanced backend development concepts in the coming weeks.
