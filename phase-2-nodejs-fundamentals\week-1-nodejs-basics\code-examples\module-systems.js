// Week 1 Day 2: Module Systems Demonstration
// This file demonstrates CommonJS and ES module patterns

console.log('=== Module Systems Demo ===\n');

// 1. CommonJS Module Examples
console.log('1. COMMONJS MODULE EXAMPLES');

// Creating a simple CommonJS module
const mathModule = {
    add: (a, b) => a + b,
    subtract: (a, b) => a - b,
    multiply: (a, b) => a * b,
    divide: (a, b) => {
        if (b === 0) throw new Error('Division by zero');
        return a / b;
    }
};

// Different export patterns
console.log('Math operations:');
console.log('5 + 3 =', mathModule.add(5, 3));
console.log('5 - 3 =', mathModule.subtract(5, 3));
console.log('5 * 3 =', mathModule.multiply(5, 3));
console.log('6 / 3 =', mathModule.divide(6, 3));

// 2. Module.exports vs exports
console.log('\n2. MODULE.EXPORTS VS EXPORTS');

// Simulating module.exports behavior
function simulateModuleExports() {
    // This is what happens internally in Node.js
    const module = { exports: {} };
    const exports = module.exports;
    
    // Method 1: Using exports (adds properties)
    exports.greet = function(name) {
        return `Hello, ${name}!`;
    };
    
    exports.farewell = function(name) {
        return `Goodbye, ${name}!`;
    };
    
    console.log('Using exports:', module.exports);
    
    // Method 2: Using module.exports (replaces entire object)
    module.exports = function(name) {
        return `Hi there, ${name}!`;
    };
    
    console.log('Using module.exports:', typeof module.exports);
    
    return module.exports;
}

const greetingFunction = simulateModuleExports();
console.log('Greeting result:', greetingFunction('Alice'));

// 3. Built-in Module Usage
console.log('\n3. BUILT-IN MODULE USAGE');

const fs = require('fs');
const path = require('path');
const os = require('os');

console.log('Current file:', __filename);
console.log('Current directory:', __dirname);
console.log('File extension:', path.extname(__filename));
console.log('Operating system:', os.platform());
console.log('CPU architecture:', os.arch());

// 4. Module Resolution Demonstration
console.log('\n4. MODULE RESOLUTION DEMO');

// Demonstrate how Node.js resolves modules
function demonstrateModuleResolution() {
    console.log('Module resolution paths:');
    console.log('require.resolve("fs"):', require.resolve('fs'));
    console.log('require.resolve("path"):', require.resolve('path'));
    
    // Show module search paths
    console.log('\nModule search paths:');
    module.paths.forEach((path, index) => {
        console.log(`${index + 1}. ${path}`);
    });
}

demonstrateModuleResolution();

// 5. Module Caching Demonstration
console.log('\n5. MODULE CACHING DEMO');

// Create a counter module simulation
function createCounterModule() {
    let count = 0;
    
    return {
        increment() {
            return ++count;
        },
        decrement() {
            return --count;
        },
        getCount() {
            return count;
        },
        reset() {
            count = 0;
            return count;
        }
    };
}

// Simulate module caching
const moduleCache = {};

function requireWithCache(moduleName) {
    if (moduleCache[moduleName]) {
        console.log(`Using cached module: ${moduleName}`);
        return moduleCache[moduleName];
    }
    
    console.log(`Loading new module: ${moduleName}`);
    const moduleInstance = createCounterModule();
    moduleCache[moduleName] = moduleInstance;
    return moduleInstance;
}

// Test module caching
const counter1 = requireWithCache('counter');
const counter2 = requireWithCache('counter'); // Should use cache

console.log('Counter 1 increment:', counter1.increment()); // 1
console.log('Counter 2 increment:', counter2.increment()); // 2 (same instance!)
console.log('Are they the same instance?', counter1 === counter2); // true

// 6. Dynamic Module Loading
console.log('\n6. DYNAMIC MODULE LOADING');

function dynamicRequire(moduleName) {
    try {
        const module = require(moduleName);
        console.log(`Successfully loaded: ${moduleName}`);
        return module;
    } catch (error) {
        console.error(`Failed to load ${moduleName}:`, error.message);
        return null;
    }
}

// Test dynamic loading
const crypto = dynamicRequire('crypto');
if (crypto) {
    const hash = crypto.createHash('md5').update('Hello World').digest('hex');
    console.log('MD5 hash of "Hello World":', hash);
}

const nonExistent = dynamicRequire('non-existent-module');

// 7. Module Pattern Examples
console.log('\n7. MODULE PATTERN EXAMPLES');

// Singleton Pattern
function createSingleton() {
    let instance = null;
    
    function Database() {
        if (instance) {
            return instance;
        }
        
        this.connection = 'Connected to database';
        this.query = function(sql) {
            return `Executing: ${sql}`;
        };
        
        instance = this;
        return this;
    }
    
    return Database;
}

const Database = createSingleton();
const db1 = new Database();
const db2 = new Database();

console.log('Database singleton test:');
console.log('Same instance?', db1 === db2); // true
console.log('Query result:', db1.query('SELECT * FROM users'));

// Factory Pattern
function createFactory() {
    function UserFactory() {}
    
    UserFactory.createUser = function(type, data) {
        switch (type) {
            case 'admin':
                return new AdminUser(data);
            case 'regular':
                return new RegularUser(data);
            default:
                throw new Error(`Unknown user type: ${type}`);
        }
    };
    
    function AdminUser(data) {
        this.name = data.name;
        this.role = 'admin';
        this.permissions = ['read', 'write', 'delete'];
    }
    
    function RegularUser(data) {
        this.name = data.name;
        this.role = 'user';
        this.permissions = ['read'];
    }
    
    return UserFactory;
}

const UserFactory = createFactory();
const admin = UserFactory.createUser('admin', { name: 'Alice' });
const user = UserFactory.createUser('regular', { name: 'Bob' });

console.log('Factory pattern test:');
console.log('Admin user:', admin);
console.log('Regular user:', user);

// 8. Module Organization Best Practices
console.log('\n8. MODULE ORGANIZATION BEST PRACTICES');

// Example of well-organized module structure
const userModule = {
    // Private variables (using closure)
    _users: [],
    _nextId: 1,
    
    // Public methods
    create(userData) {
        const user = {
            id: this._nextId++,
            ...userData,
            createdAt: new Date()
        };
        this._users.push(user);
        return user;
    },
    
    findById(id) {
        return this._users.find(user => user.id === id);
    },
    
    findByEmail(email) {
        return this._users.find(user => user.email === email);
    },
    
    getAll() {
        return [...this._users]; // Return copy to prevent mutation
    },
    
    update(id, updates) {
        const userIndex = this._users.findIndex(user => user.id === id);
        if (userIndex === -1) {
            throw new Error(`User with id ${id} not found`);
        }
        
        this._users[userIndex] = {
            ...this._users[userIndex],
            ...updates,
            updatedAt: new Date()
        };
        
        return this._users[userIndex];
    },
    
    delete(id) {
        const userIndex = this._users.findIndex(user => user.id === id);
        if (userIndex === -1) {
            throw new Error(`User with id ${id} not found`);
        }
        
        return this._users.splice(userIndex, 1)[0];
    },
    
    // Utility methods
    count() {
        return this._users.length;
    },
    
    clear() {
        this._users = [];
        this._nextId = 1;
    }
};

// Test the user module
console.log('User module test:');
const newUser = userModule.create({ name: 'John', email: '<EMAIL>' });
console.log('Created user:', newUser);

const foundUser = userModule.findById(newUser.id);
console.log('Found user:', foundUser);

const updatedUser = userModule.update(newUser.id, { name: 'John Doe' });
console.log('Updated user:', updatedUser);

console.log('Total users:', userModule.count());

// 9. Error Handling in Modules
console.log('\n9. ERROR HANDLING IN MODULES');

const safeModule = {
    divide(a, b) {
        if (typeof a !== 'number' || typeof b !== 'number') {
            throw new TypeError('Both arguments must be numbers');
        }
        
        if (b === 0) {
            throw new Error('Division by zero is not allowed');
        }
        
        return a / b;
    },
    
    parseJSON(jsonString) {
        try {
            return { success: true, data: JSON.parse(jsonString) };
        } catch (error) {
            return { success: false, error: error.message };
        }
    },
    
    asyncOperation(callback) {
        // Simulate async operation
        setTimeout(() => {
            try {
                const result = Math.random() > 0.5 ? 'Success!' : null;
                if (!result) {
                    throw new Error('Random failure occurred');
                }
                callback(null, result);
            } catch (error) {
                callback(error, null);
            }
        }, 100);
    }
};

// Test error handling
try {
    console.log('Safe division:', safeModule.divide(10, 2));
    console.log('Unsafe division:', safeModule.divide(10, 0));
} catch (error) {
    console.error('Division error:', error.message);
}

const jsonResult = safeModule.parseJSON('{"valid": "json"}');
console.log('Valid JSON parse:', jsonResult);

const invalidJsonResult = safeModule.parseJSON('invalid json');
console.log('Invalid JSON parse:', invalidJsonResult);

// Test async error handling
safeModule.asyncOperation((error, result) => {
    if (error) {
        console.error('Async operation failed:', error.message);
    } else {
        console.log('Async operation succeeded:', result);
    }
});

console.log('\n=== Module Systems Demo Complete ===');

// Export for testing
module.exports = {
    mathModule,
    userModule,
    safeModule,
    UserFactory,
    Database
};
