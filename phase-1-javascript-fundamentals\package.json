{"name": "phase-1-javascript-fundamentals", "version": "1.0.0", "description": "JavaScript fundamentals learning journey - Phase 1 of Backend Developer Roadmap", "main": "index.js", "scripts": {"test": "node tests/run-tests.js", "practice": "node utilities/setup-scripts/create-daily-files.js", "progress": "node utilities/setup-scripts/progress-tracker.js", "week1": "cd week-1-fundamentals && node code-examples/variables-demo.js", "week2": "cd week-2-functions-scope && node code-examples/function-declarations.js", "week3": "cd week-3-asynchronous-js && node code-examples/callback-examples.js", "week4": "cd week-4-modern-js-errors && node code-examples/destructuring-examples.js", "final-project": "cd final-project && node src/app.js", "lint": "eslint **/*.js", "format": "prettier --write **/*.js", "setup": "node utilities/setup-scripts/initial-setup.js"}, "keywords": ["javascript", "learning", "backend", "fundamentals", "education", "programming", "nodejs", "tutorial"], "author": "Your Name", "license": "MIT", "devDependencies": {"eslint": "^8.0.0", "prettier": "^2.8.0"}, "engines": {"node": ">=14.0.0"}, "repository": {"type": "git", "url": "https://github.com/yourusername/phase-1-javascript-fundamentals.git"}, "bugs": {"url": "https://github.com/yourusername/phase-1-javascript-fundamentals/issues"}, "homepage": "https://github.com/yourusername/phase-1-javascript-fundamentals#readme"}