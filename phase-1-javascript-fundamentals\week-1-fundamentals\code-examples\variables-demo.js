// Week 1 Day 1: Variables and Data Types Demo
// Date: [Enter today's date]

console.log("=== JavaScript Variables Demo ===");

// ===== VARIABLE DECLARATIONS =====
console.log("\n1. Variable Declarations:");

// var - function-scoped, can be redeclared and updated
var name = "<PERSON>";
console.log("var name:", name);

var name = "<PERSON>"; // Redeclaration works with var
console.log("var name (redeclared):", name);

// let - block-scoped, can be updated but not redeclared
let age = 25;
console.log("let age:", age);

age = 26; // Update works
console.log("let age (updated):", age);

// const - block-scoped, cannot be updated or redeclared
const PI = 3.14159;
console.log("const PI:", PI);

// const PI = 3.14; // This would cause an error
// PI = 3.14; // This would also cause an error

// ===== PRIMITIVE DATA TYPES =====
console.log("\n2. Primitive Data Types:");

// String
let firstName = "John";
let lastName = 'Doe';
let fullName = `${firstName} ${lastName}`; // Template literal
console.log("String examples:", firstName, lastName, fullName);

// Number
let integer = 42;
let decimal = 3.14;
let negative = -10;
let infinity = Infinity;
let notANumber = NaN;
console.log("Number examples:", integer, decimal, negative, infinity, notANumber);

// Boolean
let isActive = true;
let isCompleted = false;
console.log("Boolean examples:", isActive, isCompleted);

// Undefined
let undefinedVariable;
console.log("Undefined example:", undefinedVariable);

// Null
let emptyValue = null;
console.log("Null example:", emptyValue);

// Symbol (ES6)
let sym1 = Symbol('id');
let sym2 = Symbol('id');
console.log("Symbol examples:", sym1, sym2);
console.log("Symbols are unique:", sym1 === sym2); // false

// BigInt (ES2020)
let bigNumber = 1234567890123456789012345678901234567890n;
console.log("BigInt example:", bigNumber);

// ===== TYPE CHECKING =====
console.log("\n3. Type Checking:");

console.log('typeof "hello":', typeof "hello");
console.log('typeof 42:', typeof 42);
console.log('typeof true:', typeof true);
console.log('typeof undefined:', typeof undefined);
console.log('typeof null:', typeof null); // This returns "object" - known quirk!
console.log('typeof {}:', typeof {});
console.log('typeof []:', typeof []);
console.log('Array.isArray([]):', Array.isArray([]));

// ===== TYPE CONVERSION =====
console.log("\n4. Type Conversion:");

let numString = "123";
console.log("Original string:", numString, typeof numString);
console.log("Number(numString):", Number(numString), typeof Number(numString));
console.log("parseInt(numString):", parseInt(numString), typeof parseInt(numString));
console.log("parseFloat('123.45'):", parseFloat("123.45"), typeof parseFloat("123.45"));

let num = 123;
console.log("Original number:", num, typeof num);
console.log("String(num):", String(num), typeof String(num));

console.log("Boolean(1):", Boolean(1));
console.log("Boolean(0):", Boolean(0));
console.log("Boolean(''):", Boolean(''));
console.log("Boolean('hello'):", Boolean('hello'));

// ===== SCOPE DEMONSTRATION =====
console.log("\n5. Scope Demonstration:");

function demonstrateScope() {
    var functionVar = "I'm function-scoped";
    
    if (true) {
        let blockLet = "I'm block-scoped";
        const blockConst = "I'm also block-scoped";
        var blockVar = "I'm function-scoped even in block";
        
        console.log("Inside block - blockLet:", blockLet);
        console.log("Inside block - blockConst:", blockConst);
    }
    
    console.log("Outside block - functionVar:", functionVar);
    console.log("Outside block - blockVar:", blockVar);
    // console.log("Outside block - blockLet:", blockLet); // This would cause an error
}

demonstrateScope();

// ===== PRACTICAL EXAMPLES =====
console.log("\n6. Practical Examples:");

// User information storage
const user = {
    id: 1,
    username: "johndoe",
    email: "<EMAIL>",
    isActive: true,
    lastLogin: null
};

console.log("User object:", user);
console.log("User email:", user.email);
console.log("User is active:", user.isActive);

// Shopping cart example
let cartItems = [];
let totalPrice = 0;
const taxRate = 0.08;

cartItems.push("Apple");
cartItems.push("Banana");
cartItems.push("Orange");

console.log("Cart items:", cartItems);
console.log("Number of items:", cartItems.length);

// Temperature conversion
const celsius = 25;
const fahrenheit = (celsius * 9/5) + 32;
console.log(`${celsius}°C is ${fahrenheit}°F`);

console.log("\n=== Variables Demo Complete ===");

// Practice exercises for you to try:
console.log("\n=== Practice Exercises ===");
console.log("Try these exercises:");
console.log("1. Create variables for your personal information");
console.log("2. Practice type conversion with different values");
console.log("3. Create a simple calculator using variables");
console.log("4. Experiment with scope by creating nested functions");
