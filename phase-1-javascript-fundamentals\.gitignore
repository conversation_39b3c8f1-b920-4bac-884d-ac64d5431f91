# Node modules
node_modules/

# Logs
*.log
logs/

# Runtime data
pids/
*.pid

# IDE files
.vscode/
.idea/

# OS files
.DS_Store
Thumbs.db

# Personal notes (if you want to keep some private)
personal-notes/

# Temporary files
*.tmp
*.temp

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/
